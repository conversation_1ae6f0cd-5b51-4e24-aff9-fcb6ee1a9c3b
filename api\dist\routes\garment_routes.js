"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const garmentController_1 = __importDefault(require("../controllers/garmentController"));
const garmentsRoutes = (0, express_1.Router)();
garmentsRoutes.get('/', garmentController_1.default.getAll);
garmentsRoutes.get('/:id', garmentController_1.default.getById);
garmentsRoutes.post('/', garmentController_1.default.create);
garmentsRoutes.patch('/:id', garmentController_1.default.patch);
garmentsRoutes.delete('/:id', garmentController_1.default.delete);
exports.default = garmentsRoutes;
