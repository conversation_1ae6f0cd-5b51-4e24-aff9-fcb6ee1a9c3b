import { Router } from 'express';
import RawMaterialController from '../controllers/rawMaterialController';

const rawMaterialRoutes = Router();

rawMaterialRoutes.get('/', RawMaterialController.getAll);
rawMaterialRoutes.get('/:id', RawMaterialController.getById);
rawMaterialRoutes.post('/', RawMaterialController.create);
rawMaterialRoutes.patch('/:id', RawMaterialController.patch);
rawMaterialRoutes.delete('/:id', RawMaterialController.delete);

export default rawMaterialRoutes;
