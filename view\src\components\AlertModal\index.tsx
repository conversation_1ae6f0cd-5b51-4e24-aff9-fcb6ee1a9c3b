interface AlertModalProps {
	isOpen: boolean;
	onClose: () => void;
	type: "success" | "error" | "confirm";
	message: string;
	onConfirm?: () => void;
}

export function AlertModal({
	isOpen,
	onClose,
	type,
	message,
	onConfirm,
}: AlertModalProps) {
	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 font-roboto">
			<div className="bg-white p-8 rounded border border-gray-300 max-w-md w-full mx-4">
				<div className="text-xl font-inter mb-4 text-center whitespace-pre-line">
					{message}
				</div>

				<div className="flex justify-center gap-4">
					{type === "confirm" ? (
						<>
							<button
								type="button"
								className="border border-gray-300 rounded bg-gray-200 py-2 px-6 hover:bg-gray-300 transition-colors"
								onClick={onClose}
							>
								Cancelar
							</button>
							{onConfirm && (
								<button
									type="button"
									className="border border-gray-300 rounded bg-red-200 py-2 px-6 hover:bg-red-300 transition-colors"
									onClick={onConfirm}
								>
									Confirmar
								</button>
							)}
						</>
					) : (
						<button
							type="button"
							className={`border border-gray-300 rounded py-2 px-6 transition-colors ${type === "success"
									? "bg-green-200 hover:bg-green-300"
									: "bg-red-200 hover:bg-red-300"
								}`}
							onClick={onConfirm || onClose}
						>
							OK
						</button>
					)}
				</div>
			</div>
		</div>
	);
}
