import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { AlertModal } from "../../../components/AlertModal";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

export function NewMaterial() {
	const [materialName, setMaterialName] = useState("");
	const [manufacturer, setManufacturer] = useState("");
	const [pricePerKg, setPricePerKg] = useState("");
	const [color, setColor] = useState("");
	const [weight, setWeight] = useState("");
	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	const navigate = useNavigate();

	async function handleNewMaterial(e: React.FormEvent) {
		e.preventDefault();

		if (!materialName) {
			setAlertMessage("O nome do material está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!manufacturer) {
			setAlertMessage("O fabricante está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!pricePerKg) {
			setAlertMessage("O valor por kg está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!color) {
			setAlertMessage("A cor do material está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!weight) {
			setAlertMessage("O peso está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		try {
			// TODO: Implementar rota de materiais na API
			// await api.post("/materials", {
			// 	name: materialName,
			// 	manufacturer: manufacturer,
			// 	pricePerKg: Number.parseFloat(pricePerKg),
			// 	color: color,
			// 	weight: Number.parseFloat(weight),
			// });

			setAlertMessage("Funcionalidade de materiais ainda não implementada na API.");
			setAlertType("error");
			setIsAlertModalOpen(true);

			// // Limpa os campos após sucesso
			// setMaterialName("");
			// setManufacturer("");
			// setPricePerKg("");
			// setColor("");
			// setWeight("");

			// navigate(-1);
		} catch (error) {
			setAlertMessage("Erro ao cadastrar material.");
			setAlertType("error");
			setIsAlertModalOpen(true);
		}
	}

	return (
		<div className="min-h-screen w-screen flex flex-col">
			<Header pagename={"Nova Matéria-Prima"} href={"/"} $logout={false} />

			<main className="flex-grow flex flex-col items-center py-8 px-4">
				<h1 className="text-2xl font-bold text-center mb-8">
					Cadastre uma nova Matéria-Prima
				</h1>

				<div className="container mx-auto px-4 justify-items-center">
					<form
						id="cad-material"
						className="text-sm sm:text-base flex items-center"
					>
						<fieldset className="p-4 border border-gray-300 rounded-md flex flex-col gap-4 w-72">
							<div className="input-wrapper flex flex-col">
								<label htmlFor="material-name" className="mb-1">
									Nome do Material:
								</label>
								<input
									type="text"
									id="material-name"
									className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
									placeholder="Ex: Fio 100% Algodão"
									value={materialName}
									onChange={(e) => setMaterialName(e.target.value)}
								/>
							</div>

							<div className="input-wrapper flex flex-col">
								<label htmlFor="manufacturer" className="mb-1">
									Fabricante:
								</label>
								<input
									type="text"
									id="manufacturer"
									className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
									placeholder="Ex: Círculo S/A"
									value={manufacturer}
									onChange={(e) => setManufacturer(e.target.value)}
								/>
							</div>

							<div className="line-wrapper flex flex-col sm:flex-row justify-between gap-2 sm:gap-4">
								<div className="input-wrapper flex flex-col flex-grow">
									<label htmlFor="price-per-kg" className="mb-1">
										Valor por Kg:
									</label>
									<input
										type="number"
										id="price-per-kg"
										min={0.01}
										step="0.01"
										className="text-sm border border-gray-300 rounded px-2 py-1"
										placeholder="R$ 0,00"
										value={pricePerKg}
										onChange={(e) => setPricePerKg(e.target.value)}
									/>
								</div>

								<div className="input-wrapper flex flex-col flex-grow">
									<label htmlFor="material-color" className="mb-1">
										Cor:
									</label>
									<input
										type="text"
										id="material-color"
										className="text-sm border border-gray-300 rounded px-2 py-1"
										placeholder="Ex: Azul Marinho"
										value={color}
										onChange={(e) => setColor(e.target.value)}
									/>
								</div>

								<div className="input-wrapper flex flex-col flex-grow">
									<label htmlFor="weight" className="mb-1">
										Peso (kg):
									</label>
									<input
										type="number"
										id="weight"
										min={0.01}
										step="0.01"
										className="text-sm border border-gray-300 rounded px-2 py-1"
										placeholder="0.00"
										value={weight}
										onChange={(e) => setWeight(e.target.value)}
									/>
								</div>
							</div>

							<button
								type="submit"
								className="bg-blue-300 border border-gray-300 rounded px-4 py-2 mt-2"
							>
								Confirmar cadastro
							</button>
						</fieldset>
					</form>
				</div>
			</main>

			<Footer />

			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => setIsAlertModalOpen(false)}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</div>
	);
}
