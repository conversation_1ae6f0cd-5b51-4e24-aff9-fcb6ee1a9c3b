# Manager Malhas - Sistema de Gerenciamento para Malharias

## 📋 Índice
- [Visão Geral](#-visão-geral)
- [Arquitetura do Sistema](#-arquitetura-do-sistema)
- [Tecnologias Utilizadas](#-tecnologias-utilizadas)
- [Pré-requisitos](#-pré-requisitos)
- [Instalação e Configuração](#-instalação-e-configuração)
- [Estrutura do Projeto](#-estrutura-do-projeto)
- [Desenvolvimento](#-desenvolvimento)
- [API Documentation](#-api-documentation)
- [Banco de Dados](#-banco-de-dados)
- [Scripts Disponíveis](#-scripts-disponíveis)
- [Padrões de Código](#-padrões-de-código)
- [Troubleshooting](#-troubleshooting)
- [Contribuição](#-contribuição)

## 🎯 Visão Geral

O **Manager Malhas** é um sistema completo de gerenciamento para malharias, desenvolvido para otimizar o controle de:

- **👥 Clientes**: Cadastro e gerenciamento de clientes (pessoa física e jurídica)
- **👕 Modelos/Peças**: Catálogo de produtos com controle de estoque
- **🧵 Matérias-primas**: Gestão de materiais e fornecedores
- **📋 Pedidos**: Controle completo do fluxo de pedidos
- **👨‍💼 Usuários**: Sistema de funcionários e administradores
- **📊 Relatórios**: Controle de vendas e comissões

### Funcionalidades Principais
- ✅ Sistema de autenticação com diferentes níveis de acesso
- ✅ CRUD completo para todas as entidades
- ✅ Controle de estoque em tempo real
- ✅ Gestão de pedidos com status e prazos
- ✅ Cálculo automático de comissões
- ✅ Interface responsiva e intuitiva
- ✅ Soft delete para preservação de dados históricos

## 🏗️ Arquitetura do Sistema

O projeto segue uma arquitetura **Full Stack** com separação clara entre frontend e backend:

```
┌─────────────────┐    HTTP/REST    ┌─────────────────┐    TypeORM    ┌─────────────────┐
│                 │ ◄──────────────► │                 │ ◄────────────► │                 │
│   Frontend      │                 │    Backend      │                │     MySQL       │
│   (React)       │                 │   (Express)     │                │   Database      │
│                 │                 │                 │                │                 │
└─────────────────┘                 └─────────────────┘                └─────────────────┘
```

### Camadas da Aplicação

**Frontend (React + TypeScript)**
- **Presentation Layer**: Componentes React com Styled Components e Tailwind
- **State Management**: Context API para autenticação
- **Routing**: React Router DOM para navegação
- **HTTP Client**: Axios para comunicação com API

**Backend (Express + TypeScript)**
- **Routes Layer**: Definição de endpoints REST
- **Controllers Layer**: Lógica de controle e validação de entrada
- **Services Layer**: Regras de negócio e lógica de aplicação
- **Repository Layer**: Abstração de acesso aos dados
- **Entities Layer**: Modelos de dados com TypeORM

**Database (MySQL)**
- **Migrations**: Controle de versão do schema
- **Entities**: Mapeamento objeto-relacional
- **Relationships**: Relacionamentos entre tabelas bem definidos

## 🛠️ Tecnologias Utilizadas

### Backend
- **[Express.js](https://expressjs.com/)** - Framework web minimalista
- **[TypeORM](https://typeorm.io/)** - ORM para TypeScript/JavaScript
- **[MySQL](https://www.mysql.com/)** - Sistema de gerenciamento de banco de dados
- **[TypeScript](https://www.typescriptlang.org/)** - Superset tipado do JavaScript
- **[bcrypt](https://www.npmjs.com/package/bcrypt)** - Hash de senhas
- **[CORS](https://www.npmjs.com/package/cors)** - Cross-Origin Resource Sharing
- **[dotenv](https://www.npmjs.com/package/dotenv)** - Gerenciamento de variáveis de ambiente

### Frontend
- **[React 18](https://react.dev/)** - Biblioteca para interfaces de usuário
- **[Vite](https://vitejs.dev/)** - Build tool e dev server
- **[TypeScript](https://www.typescriptlang.org/)** - Tipagem estática
- **[React Router DOM](https://reactrouter.com/)** - Roteamento
- **[Tailwind CSS](https://tailwindcss.com/)** - Framework CSS utilitário
- **[Styled Components](https://styled-components.com/)** - CSS-in-JS
- **[Axios](https://axios-http.com/)** - Cliente HTTP
- **[React Icons](https://react-icons.github.io/react-icons/)** - Biblioteca de ícones

### Ferramentas de Desenvolvimento
- **[Biome](https://biomejs.dev/)** - Linter e formatter
- **[ts-node-dev](https://www.npmjs.com/package/ts-node-dev)** - Desenvolvimento TypeScript
- **[Knex.js](https://knexjs.org/)** - Query builder (legacy, sendo migrado para TypeORM)

## 📋 Pré-requisitos

Antes de começar, certifique-se de ter instalado:

- **Node.js** (versão 18 ou superior)
- **npm** (versão 8 ou superior)
- **MySQL** (versão 8 ou superior)
- **Git** (para controle de versão)

### Verificação dos Pré-requisitos
```bash
node --version    # v18.0.0 ou superior
npm --version     # 8.0.0 ou superior
mysql --version   # 8.0.0 ou superior
git --version     # qualquer versão recente
```

## ⚙️ Instalação e Configuração

### 1. Clone o Repositório
```bash
git clone <url-do-repositorio>
cd managermalhas
```

### 2. Configuração do Banco de Dados
```sql
-- Conecte ao MySQL e execute:
CREATE DATABASE db_malharias;
```

### 3. Configuração do Backend
```bash
cd api
npm install
```

Crie o arquivo `.env` na pasta `api`:
```env
DB_DEV_USER=seu_usuario_mysql
DB_DEV_PASS=sua_senha_mysql
```

### 4. Configuração do Frontend
```bash
cd ../view
npm install
```

### 5. Executar Migrations
```bash
cd ../api
npm run migration:run
```

### 6. Iniciar os Serviços

**Terminal 1 - Backend:**
```bash
cd api
npm run dev
```

**Terminal 2 - Frontend:**
```bash
cd view
npm run dev
```

### 7. Acessar a Aplicação
- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:3333
- **API Health Check**: http://localhost:3333/ (deve retornar "Olá! Estou funcionando com TypeScript!")

## 📁 Estrutura do Projeto

```
managermalhas/
├── api/                          # Backend (Express + TypeORM)
│   ├── src/
│   │   ├── controllers/          # Controladores da API
│   │   │   ├── userController.ts
│   │   │   ├── customerController.ts
│   │   │   ├── orderController.ts
│   │   │   ├── garmentController.ts
│   │   │   └── rawMaterialController.ts
│   │   ├── services/             # Lógica de negócio
│   │   │   ├── userService.ts
│   │   │   ├── customerService.ts
│   │   │   ├── orderService.ts
│   │   │   ├── garmentService.ts
│   │   │   └── rawMaterialService.ts
│   │   ├── repositories/         # Camada de acesso aos dados
│   │   │   ├── userRepository.ts
│   │   │   ├── customerRepository.ts
│   │   │   └── ...
│   │   ├── entities/             # Modelos TypeORM
│   │   │   ├── User.ts
│   │   │   ├── Person.ts
│   │   │   ├── Customer.ts
│   │   │   ├── Order.ts
│   │   │   ├── Garment.ts
│   │   │   └── RawMaterial.ts
│   │   ├── dto/                  # Data Transfer Objects
│   │   │   ├── UserDto.ts
│   │   │   ├── CustomerDto.ts
│   │   │   └── ...
│   │   ├── routes/               # Definição de rotas
│   │   │   ├── index.ts
│   │   │   ├── user_routes.ts
│   │   │   ├── customer_routes.ts
│   │   │   └── ...
│   │   ├── migrations/           # Migrations do banco
│   │   ├── database/             # Configuração do banco
│   │   ├── data-source.ts        # Configuração TypeORM
│   │   └── server.ts             # Servidor principal
│   ├── docs/                     # Documentação da API
│   │   ├── README.md
│   │   ├── user-routes.md
│   │   ├── customer-routes.md
│   │   └── ...
│   ├── dist/                     # Código compilado
│   ├── package.json
│   └── tsconfig.json
├── view/                         # Frontend (React + Vite)
│   ├── src/
│   │   ├── components/           # Componentes reutilizáveis
│   │   │   ├── Header/
│   │   │   ├── Footer/
│   │   │   ├── AlertModal/
│   │   │   └── ...
│   │   ├── pages/                # Páginas da aplicação
│   │   │   ├── Home/
│   │   │   ├── Login/
│   │   │   ├── ClientPages/
│   │   │   ├── OrderPages/
│   │   │   ├── ModelPages/
│   │   │   ├── StockPages/
│   │   │   └── AdminPages/
│   │   ├── routes/               # Configuração de rotas
│   │   │   ├── index.tsx
│   │   │   ├── app.routes.tsx
│   │   │   └── auth.routes.tsx
│   │   ├── hooks/                # Hooks customizados
│   │   │   └── auth.tsx
│   │   ├── service/              # Serviços de API
│   │   │   └── api.tsx
│   │   ├── assets/               # Recursos estáticos
│   │   └── main.tsx              # Ponto de entrada
│   ├── public/
│   ├── package.json
│   └── vite.config.ts
├── README.md                     # Este arquivo
└── AGENT.md                      # Comandos rápidos
```

## 🚀 Desenvolvimento

### Fluxo de Desenvolvimento Recomendado

1. **Configuração Inicial**
   ```bash
   # Clone e configure o projeto (veja seção de instalação)
   # Certifique-se de que o banco está rodando
   # Execute as migrations
   ```

2. **Desenvolvimento Diário**
   ```bash
   # Terminal 1 - Backend
   cd api && npm run dev

   # Terminal 2 - Frontend
   cd view && npm run dev

   # Terminal 3 - Para comandos adicionais (migrations, etc.)
   ```

3. **Antes de Fazer Commits**
   ```bash
   # Verifique o código com Biome
   cd api && npx biome check src/
   cd view && npx biome check src/

   # Compile o TypeScript
   cd api && npm run build
   cd view && npm run build
   ```

### Estrutura de Desenvolvimento por Módulo

Ao desenvolver uma nova funcionalidade, siga esta ordem:

1. **Backend First**
   - Criar/atualizar Entity (`api/src/entities/`)
   - Criar/atualizar DTO (`api/src/dto/`)
   - Criar/atualizar Repository (`api/src/repositories/`)
   - Criar/atualizar Service (`api/src/services/`)
   - Criar/atualizar Controller (`api/src/controllers/`)
   - Criar/atualizar Routes (`api/src/routes/`)
   - Testar endpoints via Postman/Insomnia

2. **Frontend Second**
   - Criar/atualizar Service (`view/src/service/`)
   - Criar/atualizar Components (`view/src/components/`)
   - Criar/atualizar Pages (`view/src/pages/`)
   - Atualizar Routes (`view/src/routes/`)

### Convenções de Nomenclatura

**Backend:**
- **Entities**: PascalCase (`User.ts`, `Customer.ts`)
- **Controllers**: camelCase + Controller (`userController.ts`)
- **Services**: camelCase + Service (`userService.ts`)
- **Routes**: camelCase + _routes (`user_routes.ts`)
- **DTOs**: Interface + PascalCase + Dto (`ICreateUserDto`)

**Frontend:**
- **Components**: PascalCase (`Header`, `AlertModal`)
- **Pages**: PascalCase (`Home`, `NewClient`)
- **Hooks**: camelCase (`useAuth`, `useApi`)
- **Services**: camelCase (`api.tsx`)

### Padrões de Commit

```bash
# Tipos de commit
feat: nova funcionalidade
fix: correção de bug
docs: documentação
style: formatação
refactor: refatoração
test: testes
chore: tarefas de manutenção

# Exemplos
git commit -m "feat: adicionar CRUD de produtos"
git commit -m "fix: corrigir validação de CPF"
git commit -m "docs: atualizar README com instruções"
```

## 📚 API Documentation

A documentação completa da API está disponível em `api/docs/`:

- **[📖 Visão Geral](./api/docs/README.md)** - Índice e padrões gerais
- **[👥 User Routes](./api/docs/user-routes.md)** - Gerenciamento de usuários
- **[🏢 Customer Routes](./api/docs/customer-routes.md)** - Gerenciamento de clientes
- **[👕 Garment Routes](./api/docs/garment-routes.md)** - Gerenciamento de modelos
- **[🧵 Raw Material Routes](./api/docs/raw-material-routes.md)** - Gerenciamento de matérias-primas
- **[📋 Order Routes](./api/docs/order-routes.md)** - Gerenciamento de pedidos

### Endpoints Principais

| Módulo | Base URL | Descrição |
|--------|----------|-----------|
| Users | `/users` | Funcionários e administradores |
| Customers | `/customers` | Clientes da malharia |
| Garments | `/garments` | Modelos/peças de roupa |
| Raw Materials | `/raw-materials` | Matérias-primas |
| Orders | `/orders` | Pedidos de clientes |

### Testando a API

**Usando curl:**
```bash
# Health check
curl http://localhost:3333/

# Login
curl -X POST http://localhost:3333/users/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"senha"}'

# Listar usuários
curl http://localhost:3333/users
```

**Usando Postman/Insomnia:**
- Importe a collection da pasta `api/docs/` (se disponível)
- Configure a base URL: `http://localhost:3333`
- Teste os endpoints conforme documentação

## 🗄️ Banco de Dados

### Schema Principal

O banco de dados segue um design normalizado com as seguintes entidades principais:

```sql
-- Entidades Principais
person          # Dados pessoais (CPF/CNPJ, nome, contato)
users           # Usuários do sistema (funcionários/admin)
customers       # Clientes da malharia
staff           # Dados específicos de funcionários (comissão)
garments        # Modelos/peças de roupa
raw_material    # Matérias-primas
orders          # Pedidos de clientes

-- Tabelas de Relacionamento
order_has_garments  # Relaciona pedidos com modelos (N:N)
```

### Relacionamentos

```
person (1) ──── (N) users
person (1) ──── (N) customers
users (1) ──── (1) staff
users (1) ──── (N) orders
customers (1) ──── (N) orders
orders (N) ──── (N) garments (via order_has_garments)
garments (N) ──── (N) raw_material
```

### Migrations

```bash
# Gerar nova migration
cd api
npm run migration:generate -- NomeDaMigration

# Executar migrations pendentes
npm run migration:run

# Reverter última migration (cuidado!)
npx typeorm-ts-node-commonjs -d ./src/data-source.ts migration:revert
```

### Dados Iniciais

O sistema inclui dados de exemplo em `api/src/migrations/basic/insert-inicial-datas.sql`:
- Usuário admin padrão
- Clientes de exemplo
- Modelos de exemplo
- Matérias-primas de exemplo

### Backup e Restore

```bash
# Backup
mysqldump -u usuario -p db_malharias > backup.sql

# Restore
mysql -u usuario -p db_malharias < backup.sql
```

## 📜 Scripts Disponíveis

### Backend (api/)

| Script | Comando | Descrição |
|--------|---------|-----------|
| Desenvolvimento | `npm run dev` | Inicia servidor em modo desenvolvimento |
| Build | `npm run build` | Compila TypeScript para JavaScript |
| Gerar Migration | `npm run migration:generate` | Gera nova migration |
| Executar Migrations | `npm run migration:run` | Executa migrations pendentes |
| Teste | `npm test` | Executa testes (não implementado) |

### Frontend (view/)

| Script | Comando | Descrição |
|--------|---------|-----------|
| Desenvolvimento | `npm run dev` | Inicia servidor de desenvolvimento |
| Build | `npm run build` | Gera build de produção |
| Preview | `npm run preview` | Visualiza build de produção |
| Lint | `npm run lint` | Executa linter |

### Comandos Úteis

```bash
# Limpar node_modules e reinstalar
rm -rf node_modules package-lock.json && npm install

# Verificar portas em uso
netstat -tulpn | grep :3333  # Backend
netstat -tulpn | grep :5173  # Frontend

# Verificar logs do MySQL
sudo tail -f /var/log/mysql/error.log
```

## 🎨 Padrões de Código

### Configuração do Biome

O projeto usa **Biome** para formatação e linting. As configurações estão em:
- `api/biome.json`
- `view/biome.json`

```bash
# Verificar código
npx biome check src/

# Formatar código
npx biome format src/ --write

# Aplicar correções automáticas
npx biome check src/ --apply
```

### Regras de TypeScript

- **Tipagem estrita**: Sempre use tipos explícitos
- **Interfaces**: Prefira interfaces sobre types para objetos
- **Enums**: Use enums para valores constantes
- **Null safety**: Evite `any`, use `unknown` quando necessário

**Exemplos:**
```typescript
// ✅ Bom
interface IUser {
  id: number;
  name: string;
  email?: string;
}

// ❌ Evitar
const user: any = { id: 1, name: "João" };
```

### Estrutura de Componentes React

```typescript
// Estrutura padrão de componente
import React from 'react';
import { ComponentProps } from './types';
import { Container } from './styles';

interface Props {
  title: string;
  children?: React.ReactNode;
}

export const Component: React.FC<Props> = ({ title, children }) => {
  return (
    <Container>
      <h1>{title}</h1>
      {children}
    </Container>
  );
};
```

### Padrões de API

**Controllers:**
```typescript
public async create(req: Request, res: Response) {
  try {
    // Validações
    // Lógica de negócio via Service
    // Resposta padronizada
    return res.status(201).json({ message: "Sucesso!", data: result });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Erro interno" });
  }
}
```

**Services:**
```typescript
public static async createUser(data: ICreateUserDto): Promise<User> {
  // Validações de negócio
  // Operações no banco via Repository
  // Retorno do resultado
}
```

## 🔧 Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão com Banco
```bash
Error: ER_ACCESS_DENIED_ERROR: Access denied for user
```
**Solução:**
- Verifique as credenciais no arquivo `.env`
- Confirme se o usuário MySQL tem permissões
- Teste a conexão: `mysql -u usuario -p`

#### 2. Porta já em uso
```bash
Error: listen EADDRINUSE: address already in use :::3333
```
**Solução:**
```bash
# Encontrar processo usando a porta
lsof -ti:3333

# Matar processo
kill -9 $(lsof -ti:3333)

# Ou usar porta diferente
PORT=3334 npm run dev
```

#### 3. Erro de Migration
```bash
QueryFailedError: Table 'users' already exists
```
**Solução:**
- Verifique se as migrations já foram executadas
- Reverta migrations se necessário
- Recrie o banco se estiver em desenvolvimento

#### 4. Erro de CORS
```bash
Access to XMLHttpRequest blocked by CORS policy
```
**Solução:**
- Verifique se o CORS está configurado no backend
- Confirme se as URLs estão corretas
- Verifique se o backend está rodando

#### 5. Dependências Desatualizadas
```bash
npm WARN deprecated package@version
```
**Solução:**
```bash
# Verificar dependências desatualizadas
npm outdated

# Atualizar dependências
npm update

# Auditoria de segurança
npm audit
npm audit fix
```

### Logs e Debug

**Backend:**
```bash
# Logs detalhados do TypeORM
# Adicione no data-source.ts:
logging: true,
logger: "advanced-console"

# Debug do ts-node-dev
DEBUG=* npm run dev
```

**Frontend:**
```bash
# Debug do Vite
DEBUG=vite:* npm run dev

# Verificar build
npm run build && npm run preview
```

### Performance

**Backend:**
- Use índices no banco para consultas frequentes
- Implemente paginação para listas grandes
- Use connection pooling do TypeORM
- Cache consultas repetitivas

**Frontend:**
- Use React.memo para componentes pesados
- Implemente lazy loading para rotas
- Otimize imagens e assets
- Use debounce em campos de busca

## 🤝 Contribuição

### Como Contribuir

1. **Fork o projeto**
2. **Crie uma branch para sua feature**
   ```bash
   git checkout -b feature/nova-funcionalidade
   ```
3. **Faça suas alterações seguindo os padrões**
4. **Teste suas alterações**
5. **Commit suas mudanças**
   ```bash
   git commit -m "feat: adicionar nova funcionalidade"
   ```
6. **Push para sua branch**
   ```bash
   git push origin feature/nova-funcionalidade
   ```
7. **Abra um Pull Request**

### Checklist para Pull Requests

- [ ] Código segue os padrões estabelecidos
- [ ] Testes passam (quando implementados)
- [ ] Documentação atualizada
- [ ] Sem erros de TypeScript
- [ ] Biome check passou
- [ ] Funcionalidade testada manualmente
- [ ] Migrations criadas se necessário

### Estrutura de Issues

**Bug Report:**
```markdown
## Descrição do Bug
Descrição clara do problema

## Passos para Reproduzir
1. Vá para '...'
2. Clique em '...'
3. Veja o erro

## Comportamento Esperado
O que deveria acontecer

## Screenshots
Se aplicável

## Ambiente
- OS: [Windows/Mac/Linux]
- Browser: [Chrome/Firefox/Safari]
- Versão: [versão do projeto]
```

**Feature Request:**
```markdown
## Descrição da Feature
Descrição clara da funcionalidade

## Motivação
Por que esta feature é necessária

## Solução Proposta
Como você imagina que deveria funcionar

## Alternativas Consideradas
Outras abordagens que você considerou
```

### Roadmap

**Próximas Funcionalidades:**
- [ ] Sistema de relatórios avançados
- [ ] API de notificações
- [ ] Dashboard com gráficos
- [ ] Integração com sistemas de pagamento
- [ ] App mobile (React Native)
- [ ] Sistema de backup automático
- [ ] Testes automatizados
- [ ] CI/CD pipeline

**Melhorias Técnicas:**
- [ ] Migração completa para TypeORM (remover Knex)
- [ ] Implementar autenticação JWT
- [ ] Adicionar rate limiting
- [ ] Implementar cache Redis
- [ ] Dockerização completa
- [ ] Monitoramento e logs estruturados

---

## 📞 Suporte

Para dúvidas, problemas ou sugestões:

1. **Consulte esta documentação primeiro**
2. **Verifique as issues existentes**
3. **Consulte a documentação da API em `api/docs/`**
4. **Abra uma nova issue se necessário**

---

**Desenvolvido com ❤️ para otimizar o gerenciamento de malharias**

*Última atualização: Dezembro 2024*