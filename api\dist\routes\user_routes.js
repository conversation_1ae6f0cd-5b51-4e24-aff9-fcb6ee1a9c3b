"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const userController_1 = __importDefault(require("../controllers/userController"));
const usersRoutes = (0, express_1.Router)();
usersRoutes.get('/', userController_1.default.getAll);
usersRoutes.get('/:id', userController_1.default.getById);
usersRoutes.post('/', userController_1.default.create);
usersRoutes.post('/login', userController_1.default.login);
usersRoutes.patch('/:id', userController_1.default.patch);
usersRoutes.delete('/:id', userController_1.default.delete);
exports.default = usersRoutes;
