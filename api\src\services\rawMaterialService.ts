import { ICreateRawMaterialDto, IUpdateRawMaterialDto } from '../dto/RawMaterialDto';
import { RawMaterial } from '../entities/RawMaterial';
import { rawMaterialRepository } from '../repositories/rawMaterialRepository';

export class RawMaterialService {
    
    public static async getAllRawMaterials(): Promise<RawMaterial[]> {
        try {
            const rawMaterials = await rawMaterialRepository.find({
                where: { is_active: true }
            });
            return rawMaterials;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async getRawMaterialById(rawMaterialId: number): Promise<RawMaterial | null> {
        try {
            const rawMaterial = await rawMaterialRepository.findOne({
                where: { id: rawMaterialId, is_active: true }
            });
            return rawMaterial || null;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async createRawMaterial(rawMaterialData: {
        name: string;
        manufacturer: string;
        price_per_weight: number;
        color: string;
        weight: number;
    }): Promise<RawMaterial> {
        try {
            const newRawMaterialData: ICreateRawMaterialDto = {
                name: rawMaterialData.name,
                manufacturer: rawMaterialData.manufacturer,
                price_per_weight: rawMaterialData.price_per_weight,
                color: rawMaterialData.color,
                weight: rawMaterialData.weight,
                is_active: true
            };

            const newRawMaterial = rawMaterialRepository.create(newRawMaterialData);
            const savedRawMaterial = await rawMaterialRepository.save(newRawMaterial);
            return savedRawMaterial;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async updateRawMaterial(rawMaterialId: number, updateData: IUpdateRawMaterialDto): Promise<RawMaterial | null> {
        try {
            const rawMaterial = await rawMaterialRepository.findOne({
                where: { id: rawMaterialId, is_active: true }
            });

            if (!rawMaterial) {
                return null;
            }

            await rawMaterialRepository.update(rawMaterialId, updateData);
            
            const updatedRawMaterial = await rawMaterialRepository.findOneBy({ id: rawMaterialId });
            return updatedRawMaterial || null;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async deleteRawMaterial(rawMaterialId: number): Promise<boolean> {
        try {
            const rawMaterial = await rawMaterialRepository.findOne({
                where: { id: rawMaterialId, is_active: true }
            });

            if (!rawMaterial) {
                return false;
            }

            await rawMaterialRepository.update(rawMaterialId, { is_active: false });
            return true;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    public static async validateRawMaterialData(name: string, manufacturer: string, price_per_weight: number, color: string, weight: number): Promise<boolean> {
        if (!name || !manufacturer || price_per_weight === undefined || !color || weight === undefined) {
            return false;
        }
        
        if (price_per_weight <= 0 || weight <= 0) {
            return false;
        }
        
        return true;
    }
}
