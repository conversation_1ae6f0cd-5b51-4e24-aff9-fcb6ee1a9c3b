"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GarmentService = void 0;
const garmentRepository_1 = require("../repositories/garmentRepository");
class GarmentService {
    static getAllGarments() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const garments = yield garmentRepository_1.garmentRepository.find();
                return garments;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static getGarmentById(garmentId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const garment = yield garmentRepository_1.garmentRepository.findOneBy({ id: garmentId });
                return garment || null;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static createGarment(garmentData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if refcode already exists
                const garmentByRefCode = yield garmentRepository_1.garmentRepository.findOneBy({ refcode: garmentData.refcode });
                if (garmentByRefCode) {
                    throw new Error('Código de referência (Ref Code) já registrado em outro modelo anterior!');
                }
                const newGarmentData = {
                    name: garmentData.name,
                    refcode: garmentData.refcode,
                    price: garmentData.price,
                    size: garmentData.size,
                    color: garmentData.color,
                    in_stock: garmentData.in_stock || 0,
                    is_active: true
                };
                const newGarment = garmentRepository_1.garmentRepository.create(newGarmentData);
                const savedGarment = yield garmentRepository_1.garmentRepository.save(newGarment);
                return savedGarment;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static updateGarment(garmentId, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const existingGarment = yield garmentRepository_1.garmentRepository.findOneBy({ id: garmentId });
                if (!existingGarment) {
                    return false;
                }
                const garmentUpdatedData = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (updateData.name && { name: updateData.name })), (updateData.refcode && { refcode: updateData.refcode })), (updateData.price !== undefined && { price: updateData.price })), (updateData.size && { size: updateData.size })), (updateData.color && { color: updateData.color })), (updateData.in_stock !== undefined && { in_stock: updateData.in_stock }));
                if (!Object.keys(garmentUpdatedData).length) {
                    return false;
                }
                // Check if refcode is being updated and if it already exists
                if (garmentUpdatedData.refcode) {
                    const garmentByRefCode = yield garmentRepository_1.garmentRepository.findOneBy({ refcode: garmentUpdatedData.refcode });
                    if (garmentByRefCode && garmentByRefCode.id !== garmentId) {
                        throw new Error('Código de referência (Ref Code) já registrado em outro modelo!');
                    }
                }
                yield garmentRepository_1.garmentRepository.update(garmentId, garmentUpdatedData);
                return true;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static deleteGarment(garmentId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const garment = yield garmentRepository_1.garmentRepository.findOneBy({ id: garmentId });
                if (!garment) {
                    return false;
                }
                yield garmentRepository_1.garmentRepository.update(garmentId, { is_active: false });
                return true;
            }
            catch (error) {
                console.error(error);
                return false;
            }
        });
    }
    static checkRefCode(refcode) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const garment = yield garmentRepository_1.garmentRepository.findOneBy({ refcode });
                return !!garment;
            }
            catch (error) {
                console.error('Error checking refcode:', error);
                throw error;
            }
        });
    }
    static validateGarmentData(name, refcode, price, size, color) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!name || !refcode || !price || !size || !color) {
                return false;
            }
            return true;
        });
    }
}
exports.GarmentService = GarmentService;
