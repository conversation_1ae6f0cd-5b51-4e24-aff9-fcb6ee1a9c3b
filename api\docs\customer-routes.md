# Customer API Routes

## Base URL
- `/customers` - <PERSON><PERSON> as rotas seguem o padrão RESTful

## Endpoints

### 1. GET /customers
**Descrição:** Busca todos os clientes ativos

**Resposta de Sucesso (200):**
```json
[
  {
    "id": 1,
    "address": "Rua das Flores, 123 - Centro",
    "is_active": true,
    "person": {
      "id": 1,
      "fullname": "<PERSON>",
      "contact": "(11) 98765-4321",
      "cpf": "123.456.789-00",
      "cnpj": null,
      "personType": "individual"
    }
  }
]
```

### 2. GET /customers/:id
**Descrição:** Busca um cliente específico por ID

**Parâmetros:**
- `id` (number) - ID do cliente

**Resposta de Sucesso (200):**
```json
{
  "id": 1,
  "address": "Rua das Flores, 123 - Centro",
  "is_active": true,
  "person": {
    "id": 1,
    "fullname": "<PERSON>",
    "contact": "(11) 98765-4321",
    "cpf": "123.456.789-00",
    "cnpj": null,
    "personType": "individual"
  }
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Cliente não encontrado"
}
```

### 3. POST /customers
**Descrição:** Cria um novo cliente

**Body:**
```json
{
  "fullname": "Maria Silva",
  "contact": "(11) 98765-4321",
  "cpf": "123.456.789-00",
  "cnpj": null,
  "personType": "individual",
  "address": "Rua das Flores, 123 - Centro"
}
```

**Resposta de Sucesso (201):**
```json
{
  "message": "Cliente criado com sucesso!",
  "customer": {
    "id": 1,
    "address": "Rua das Flores, 123 - Centro",
    "is_active": true,
    "person": {
      "id": 1,
      "fullname": "Maria Silva",
      "contact": "(11) 98765-4321",
      "cpf": "123.456.789-00",
      "cnpj": null,
      "personType": "individual"
    }
  }
}
```

**Resposta de Erro (400):**
```json
{
  "error": "Há campo(s) não preenchido(s)."
}
```

**Resposta de Erro (409):**
```json
{
  "error": "CPF/CNPJ já cadastrado no sistema anteriormente"
}
```

### 4. PATCH /customers/:id
**Descrição:** Atualiza um cliente existente

**Parâmetros:**
- `id` (number) - ID do cliente

**Body (campos opcionais):**
```json
{
  "fullname": "Maria Silva Santos",
  "contact": "(11) 98765-4321",
  "address": "Rua das Rosas, 456 - Jardim"
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Cliente atualizado com sucesso!",
  "customer": {
    "id": 1,
    "address": "Rua das Rosas, 456 - Jardim",
    "is_active": true,
    "person": {
      "id": 1,
      "fullname": "Maria Silva Santos",
      "contact": "(11) 98765-4321",
      "cpf": "123.456.789-00",
      "cnpj": null,
      "personType": "individual"
    }
  }
}
```

### 5. DELETE /customers/:id
**Descrição:** Remove um cliente (soft delete - marca como inativo)

**Parâmetros:**
- `id` (number) - ID do cliente

**Resposta de Sucesso (200):**
```json
{
  "message": "Cliente removido com sucesso!"
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Cliente não encontrado"
}
```

## Códigos de Erro Comuns

- **400 Bad Request:** Dados inválidos ou campos obrigatórios não preenchidos
- **404 Not Found:** Cliente não encontrado
- **409 Conflict:** CPF/CNPJ já cadastrado
- **500 Internal Server Error:** Erro interno do servidor

## Validações

### Campos Obrigatórios (POST):
- `fullname` (string)
- `contact` (string)
- `cpf` OU `cnpj` (string) - pelo menos um deve ser fornecido
- `personType` (enum: "individual" | "legal")
- `address` (string)

### Campos Opcionais (PATCH):
- Todos os campos são opcionais
- Valores devem seguir as mesmas regras de validação quando fornecidos

### Regras de Negócio:
- CPF deve ser fornecido para personType "individual"
- CNPJ deve ser fornecido para personType "legal"
- CPF/CNPJ devem ser únicos no sistema
- Address pode ter até 200 caracteres
