import { Navigate, Route, Routes } from "react-router-dom";

import { Home } from "./../pages/Home";

import { ClientList } from "../pages/ClientPages/ClientList";
import { NewClient } from "../pages/ClientPages/NewClient";

import { ModelList } from "../pages/ModelPages/ModelList";
import { NewModel } from "../pages/ModelPages/NewModel";

import { UpdateOrder } from "../pages/OrderPages/UpdateOrder";
import { NewOrder } from "./../pages/OrderPages/NewOrder";
import { OrderList } from "./../pages/OrderPages/OrderList";

import { NewStaff } from "../pages/AdminPages/NewStaff";
import { SalesList } from "../pages/AdminPages/SalesList";
import { StaffList } from "../pages/AdminPages/StaffList";
import { UpdateStaff } from "../pages/AdminPages/UpdateStaff";

import { AdminArea } from "../pages/AdminPages/AdminArea";
import { NotFound } from "../pages/NotFound";
import { NewMaterial } from "../pages/StockPages/NewMaterial";
import { StockControl } from "../pages/StockPages/StockControl";

interface User {
	id: number;
	username: string;
	userType: "staff" | "admin";
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
	staff?: {
		id: number;
		comissionRate: string;
	};
}

export function UserRoutes() {
	let user: User | null = null;

	const storedUser = localStorage.getItem("@managermalhas:user");

	if (storedUser) {
		try {
			user = JSON.parse(storedUser) as User;
		} catch (error) {
			console.error("Erro ao fazer o parse do usuário:", error);
		}
	}

	return (
		<Routes>
			<Route path="/" element={<Navigate to="/home" replace />} />
			<Route path="/home" element={<Home />} />
			<Route path="/novo-pedido" element={<NewOrder />} />
			<Route path="/novo-cliente" element={<NewClient />} />
			<Route path="/novo-modelo" element={<NewModel />} />
			<Route path="/lista-de-pedidos" element={<OrderList />} />
			<Route path="/lista-de-clientes" element={<ClientList />} />
			<Route path="/lista-de-modelos" element={<ModelList />} />
			<Route path="/atualizar-pedido/:order_id" element={<UpdateOrder />} />
			<Route path="/estoque" element={<StockControl />} />
			<Route path="/nova-materia-prima" element={<NewMaterial />} />

			{user?.userType === "staff" ? (
				<>
					<Route path="/novo-funcionario" element={<Home />} />
					<Route path="/lista-de-funcionarios" element={<Home />} />
					<Route path="/lista-de-vendas" element={<Home />} />
					<Route path="/atualizar-perfil" element={<Home />} />
				</>
			) : (
				<>
					<Route path="/novo-funcionario" element={<NewStaff />} />
					<Route path="/lista-de-vendas" element={<SalesList />} />
					<Route path="/lista-de-funcionarios" element={<StaffList />} />
					<Route path="/atualizar-perfil/:id" element={<UpdateStaff />} />
				</>
			)}

			<Route path="/area-administrativa" element={<AdminArea />} />

			<Route path="*" element={<NotFound />} />
		</Routes>
	);
}
