import { Router } from 'express';
import StockController from '../controllers/stockController';

const stockRoutes = Router();

// Atualiza o estoque de uma única peça
stockRoutes.patch('/update', StockController.updateStock);

// Atualiza o estoque de múltiplas peças em lote
stockRoutes.patch('/batch-update', StockController.batchUpdateStock);

// Valida a disponibilidade de estoque para conclusão de pedido
stockRoutes.post('/validate-order', StockController.validateOrderStock);

// Obtém o status atual do estoque para múltiplas peças
stockRoutes.post('/status', StockController.getStockStatus);

export default stockRoutes;
