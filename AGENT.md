# Manager Malhas Project

## Commands

### Backend (API)
- Dev server: `cd api && npm run dev`
- Build: `cd api && npm run build`
- Migration generate: `cd api && npm run migration:generate`
- Migration run: `cd api && npm run migration:run`

### Frontend (View)
- Dev server: `cd view && npm run dev`
- Build: `cd view && npm run build`
- Lint: `cd view && npm run lint`
- Preview: `cd view && npm run preview`

## Code Style
- Uses Biome for formatting and linting
- TypeScript for type safety
- Follow existing patterns in codebase

## Project Structure
- `/api` - Backend API (Express + TypeORM + MySQL)
- `/view` - Frontend React app (Vite + React + Tailwind CSS)

## Tech Stack
- **Backend**: Express.js, TypeORM, MySQL, TypeScript
- **Frontend**: React, Vite, Tailwind CSS, Styled Components
- **Database**: MySQL with TypeORM migrations
- **Tools**: Biome (linting/formatting), ts-node-dev (dev)

## Development Workflow
1. Start backend: `cd api && npm run dev`
2. Start frontend: `cd view && npm run dev`
3. Check .env files for database configuration
