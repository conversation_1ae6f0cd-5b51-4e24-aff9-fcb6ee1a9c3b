"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const garmentService_1 = require("../services/garmentService");
class GarmentController {
    getAll(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const allGarments = yield garmentService_1.GarmentService.getAllGarments();
                res.status(200).json(allGarments);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    getById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const garmentId = parseInt(id, 10);
            if (isNaN(garmentId)) {
                return res.status(400).json({ error: 'ID de modelo inválido.' });
            }
            try {
                const garment = yield garmentService_1.GarmentService.getGarmentById(garmentId);
                if (!garment) {
                    return res.status(404).json({ error: 'Modelo não encontrado!' });
                }
                res.status(200).json(garment);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    create(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { name, refcode, price, size, color, in_stock } = req.body;
            if (!(yield garmentService_1.GarmentService.validateGarmentData(name, refcode, price, size, color))) {
                return res.status(400).json({ error: 'Há campo(s) não preenchido(s)!' });
            }
            try {
                const savedGarment = yield garmentService_1.GarmentService.createGarment({
                    name,
                    refcode,
                    price,
                    size,
                    color,
                    in_stock
                });
                return res.status(201).json({
                    message: 'Modelo registrado com sucesso!',
                    Garment: savedGarment
                });
            }
            catch (error) {
                console.error(error);
                if (error instanceof Error && error.message.includes('Código de referência')) {
                    return res.status(400).json({ error: error.message });
                }
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    patch(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const garmentId = parseInt(id, 10);
            if (isNaN(garmentId)) {
                return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
            }
            const { name, refcode, price, size, color, in_stock } = req.body;
            if (!name && !refcode && price === undefined && !size && !color && in_stock === undefined) {
                return res.status(400).json({ error: 'Nenhum campo para atualizar!' });
            }
            try {
                const updated = yield garmentService_1.GarmentService.updateGarment(garmentId, {
                    name,
                    refcode,
                    price,
                    size,
                    color,
                    in_stock
                });
                if (updated) {
                    return res.status(200).json({ message: 'Modelo atualizado com sucesso!' });
                }
                else {
                    return res.status(404).json({ error: "Modelo não encontrado!" });
                }
            }
            catch (error) {
                console.error(error);
                if (error instanceof Error && error.message.includes('Código de referência')) {
                    return res.status(400).json({ error: error.message });
                }
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    delete(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const garmentId = parseInt(id, 10);
            if (isNaN(garmentId)) {
                return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
            }
            try {
                const deleted = yield garmentService_1.GarmentService.deleteGarment(garmentId);
                if (deleted) {
                    return res.status(200).json({ message: 'Modelo deletado com sucesso!' });
                }
                else {
                    return res.status(404).json({ error: 'Modelo não encontrado!' });
                }
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
}
exports.default = new GarmentController();
