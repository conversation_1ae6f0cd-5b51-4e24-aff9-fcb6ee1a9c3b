import type { Request, Response } from 'express';
import { GarmentService } from "../services/garmentService";

class GarmentController {
    public async getAll(req: Request, res: Response) {
        try {
            const allGarments = await GarmentService.getAllGarments();
            res.status(200).json(allGarments);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async getById(req: Request, res: Response) {
        const { id } = req.params;
        const garmentId = parseInt(id, 10);

        if (isNaN(garmentId)) {
            return res.status(400).json({ error: 'ID de modelo inválido.' });
        }

        try {
            const garment = await GarmentService.getGarmentById(garmentId);

            if (!garment) {
                return res.status(404).json({ error: 'Modelo não encontrado!' });
            }

            res.status(200).json(garment);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async create(req: Request, res: Response) {
        const { name, refcode, price, size, color, in_stock } = req.body;

        if (!await GarmentService.validateGarmentData(name, refcode, price, size, color)) {
            return res.status(400).json({ error: 'Há campo(s) não preenchido(s)!' });
        }

        try {
            const savedGarment = await GarmentService.createGarment({
                name,
                refcode,
                price,
                size,
                color,
                in_stock
            });

            return res.status(201).json({
                message: 'Modelo registrado com sucesso!',
                Garment: savedGarment
            });
        } catch (error) {
            console.error(error);
            if (error instanceof Error && error.message.includes('Código de referência')) {
                return res.status(400).json({ error: error.message });
            }
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async patch(req: Request, res: Response) {
        const { id } = req.params;
        const garmentId = parseInt(id, 10);

        if (isNaN(garmentId)) {
            return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
        }

        const { name, refcode, price, size, color, in_stock } = req.body;

        if (!name && !refcode && price === undefined && !size && !color && in_stock === undefined) {
            return res.status(400).json({ error: 'Nenhum campo para atualizar!' });
        }

        try {
            const updated = await GarmentService.updateGarment(garmentId, {
                name,
                refcode,
                price,
                size,
                color,
                in_stock
            });

            if (updated) {
                return res.status(200).json({ message: 'Modelo atualizado com sucesso!' });
            } else {
                return res.status(404).json({ error: "Modelo não encontrado!" });
            }
        } catch (error) {
            console.error(error);
            if (error instanceof Error && error.message.includes('Código de referência')) {
                return res.status(400).json({ error: error.message });
            }
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async delete(req: Request, res: Response) {
        const { id } = req.params;
        const garmentId = parseInt(id, 10);

        if (isNaN(garmentId)) {
            return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
        }

        try {
            const deleted = await GarmentService.deleteGarment(garmentId);

            if (deleted) {
                return res.status(200).json({ message: 'Modelo deletado com sucesso!' });
            } else {
                return res.status(404).json({ error: 'Modelo não encontrado!' });
            }
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }
}

export default new GarmentController();