"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonService = void 0;
const Person_1 = require("../entities/Person");
const personRepository_1 = require("../repositories/personRepository");
class PersonService {
    static registerPerson(personData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const person = new Person_1.Person();
                person.fullname = personData.fullname;
                person.contact = personData.contact || '';
                person.personType = personData.personType || Person_1.PersonType.INDIVIDUAL;
                person.cpf = personData.cpf || '';
                person.cnpj = personData.cnpj || '';
                const savedPerson = yield personRepository_1.personRepository.save(person);
                return savedPerson.id;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static findPersonById(personId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const person = yield personRepository_1.personRepository.findOneBy({ id: personId });
                return person || null;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static getAllPersons() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const persons = yield personRepository_1.personRepository.find();
                return persons;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static updatePerson(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const person = yield personRepository_1.personRepository.findOneBy({ id });
                if (!person) {
                    return null;
                }
                if (updateData.fullname !== undefined) {
                    person.fullname = updateData.fullname;
                }
                if (updateData.contact !== undefined) {
                    person.contact = updateData.contact;
                }
                if (updateData.personType !== undefined) {
                    person.personType = updateData.personType;
                }
                if (updateData.cpf !== undefined) {
                    person.cpf = updateData.cpf;
                }
                if (updateData.cnpj !== undefined) {
                    person.cnpj = updateData.cnpj;
                }
                const updatedPerson = yield personRepository_1.personRepository.save(person);
                return updatedPerson;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static deletePerson(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield personRepository_1.personRepository.delete(id);
                return result.affected ? result.affected > 0 : false;
            }
            catch (error) {
                console.error(error);
                return false;
            }
        });
    }
    static checkCPForCNPJ(dataType, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const whereCondition = dataType === 'cpf' ? { cpf: data } : { cnpj: data };
                const person = yield personRepository_1.personRepository.findOne({
                    where: whereCondition
                });
                return !!person;
            }
            catch (error) {
                console.error(`Error getting CPF or CNPJ: ${error}`);
                throw new Error("Error getting CPF or CNPJ!");
            }
        });
    }
}
exports.PersonService = PersonService;
