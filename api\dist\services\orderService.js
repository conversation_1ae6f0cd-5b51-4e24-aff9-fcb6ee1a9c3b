"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderService = void 0;
const Order_1 = require("../entities/Order");
const OrderHasGarments_1 = require("../entities/OrderHasGarments");
const customerRepository_1 = require("../repositories/customerRepository");
const garmentRepository_1 = require("../repositories/garmentRepository");
const orderHasGamentsRepository_1 = require("../repositories/orderHasGamentsRepository");
const orderRepository_1 = require("../repositories/orderRepository");
const userRepository_1 = require("../repositories/userRepository");
class OrderService {
    static getAllOrders() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const orders = yield orderRepository_1.orderRepository.find({
                    relations: {
                        customer: {
                            person: true
                        },
                        user: {
                            person: true
                        },
                        garments: {
                            garment: true
                        }
                    }
                });
                return orders;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
    static getAllOrdersFormatted() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const orders = yield orderRepository_1.orderRepository.find({
                    relations: {
                        customer: {
                            person: true
                        },
                        user: {
                            person: true
                        },
                        garments: {
                            garment: true
                        }
                    }
                });
                // Transform orders to the requested format
                const formattedOrders = orders.map(order => ({
                    id: order.id,
                    date: order.date,
                    time: order.time,
                    deadline: order.deadline,
                    status: order.status,
                    garments: order.garments.map(garment => ({
                        id: garment.garment.id,
                        quantity: garment.quantity
                    })),
                    user: {
                        id: order.user.id,
                        username: order.user.username,
                        password: order.user.password,
                        userType: order.user.userType,
                        is_active: order.user.is_active
                    },
                    customer: {
                        id: order.customer.id,
                        address: order.customer.address,
                        is_active: order.customer.is_active
                    }
                }));
                return formattedOrders;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
    static getOrderById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const order = yield orderRepository_1.orderRepository.findOne({
                    where: { id },
                    relations: {
                        customer: {
                            person: true
                        },
                        user: {
                            person: true
                        },
                        garments: {
                            garment: true
                        }
                    }
                });
                return order;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
    static createOrder(orderData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Validate customer exists
                const customer = yield customerRepository_1.customerRepository.findOneBy({ id: orderData.customer.id });
                if (!customer) {
                    throw new Error(`Customer with id ${orderData.customer.id} not found`);
                }
                // Validate user exists
                const user = yield userRepository_1.userRepository.findOneBy({ id: orderData.user.id });
                if (!user) {
                    throw new Error(`User with id ${orderData.user.id} not found`);
                }
                // Create order
                const order = new Order_1.Order();
                order.date = orderData.date;
                order.time = orderData.time;
                order.deadline = orderData.deadline;
                order.status = orderData.status;
                order.customer = customer;
                order.user = user;
                const savedOrder = yield orderRepository_1.orderRepository.save(order);
                // Create order-garment relationships
                for (const garmentData of orderData.garments) {
                    const garment = yield garmentRepository_1.garmentRepository.findOneBy({ id: garmentData.id });
                    if (!garment) {
                        throw new Error(`Garment with id ${garmentData.id} not found`);
                    }
                    const orderHasGarment = new OrderHasGarments_1.OrderHasGarments();
                    orderHasGarment.order = savedOrder;
                    orderHasGarment.garment = garment;
                    orderHasGarment.order_id = savedOrder.id;
                    orderHasGarment.garment_id = garment.id;
                    orderHasGarment.quantity = garmentData.quantity;
                    yield orderHasGamentsRepository_1.orderHasGarmentsRepository.save(orderHasGarment);
                }
                return savedOrder;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static createOrderFromRequest(requestData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Validate required fields
                if (!requestData.deadline) {
                    throw new Error('Prazo não foi definido!');
                }
                if (!requestData.status) {
                    throw new Error('Status não foi definido!');
                }
                if (!Array.isArray(requestData.garments) || requestData.garments.length === 0) {
                    throw new Error('É necessário incluir ao menos 1 modelo no pedido!');
                }
                // Validate customer exists
                const customer = yield customerRepository_1.customerRepository.findOneBy({ id: requestData.customer_id });
                if (!customer) {
                    throw new Error('Cliente não encontrado!');
                }
                // Validate user exists
                const staff = yield userRepository_1.userRepository.findOneBy({ id: requestData.staff_id });
                if (!staff) {
                    throw new Error('Funcionário não encontrado!');
                }
                const date = new Date();
                const time = new Date().toLocaleTimeString();
                const orderData = {
                    customer,
                    user: staff,
                    date,
                    time,
                    deadline: requestData.deadline,
                    status: requestData.status,
                    garments: requestData.garments
                };
                return yield this.createOrder(orderData);
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static updateOrder(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const order = yield orderRepository_1.orderRepository.findOne({
                    where: { id },
                    relations: {
                        customer: true,
                        user: true,
                        garments: true
                    }
                });
                if (!order) {
                    return null;
                }
                // Update order fields
                if (updateData.date !== undefined) {
                    order.date = updateData.date;
                }
                if (updateData.time !== undefined) {
                    order.time = updateData.time;
                }
                if (updateData.deadline !== undefined) {
                    order.deadline = updateData.deadline;
                }
                if (updateData.status !== undefined) {
                    order.status = updateData.status;
                }
                // Update customer if provided
                if (updateData.customer_id !== undefined) {
                    const customer = yield customerRepository_1.customerRepository.findOneBy({ id: updateData.customer_id });
                    if (!customer) {
                        throw new Error(`Customer with id ${updateData.customer_id} not found`);
                    }
                    order.customer = customer;
                }
                // Update user if provided
                if (updateData.staff_id !== undefined) {
                    const user = yield userRepository_1.userRepository.findOneBy({ id: updateData.staff_id });
                    if (!user) {
                        throw new Error(`User with id ${updateData.staff_id} not found`);
                    }
                    order.user = user;
                }
                const updatedOrder = yield orderRepository_1.orderRepository.save(order);
                return updatedOrder;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static deleteOrder(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // First delete order-garment relationships
                yield orderHasGamentsRepository_1.orderHasGarmentsRepository.delete({ order_id: id });
                // Then delete the order
                const result = yield orderRepository_1.orderRepository.delete(id);
                return result.affected ? result.affected > 0 : false;
            }
            catch (error) {
                console.error(error);
                return false;
            }
        });
    }
    static getOrdersByCustomerId(customerId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const orders = yield orderRepository_1.orderRepository.find({
                    where: { customer: { id: customerId } },
                    relations: {
                        customer: {
                            person: true
                        },
                        user: {
                            person: true
                        },
                        garments: {
                            garment: true
                        }
                    }
                });
                return orders;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
    static getOrdersByUserId(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const orders = yield orderRepository_1.orderRepository.find({
                    where: { user: { id: userId } },
                    relations: {
                        customer: {
                            person: true
                        },
                        user: {
                            person: true
                        },
                        garments: {
                            garment: true
                        }
                    }
                });
                return orders;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
    static getOrdersByStatus(status) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const orders = yield orderRepository_1.orderRepository.find({
                    where: { status },
                    relations: {
                        customer: {
                            person: true
                        },
                        user: {
                            person: true
                        },
                        garments: {
                            garment: true
                        }
                    }
                });
                return orders;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
}
exports.OrderService = OrderService;
