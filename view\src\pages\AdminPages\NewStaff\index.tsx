import { useState } from "react";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

import MaskedInput from "react-text-mask";

import { useNavigate } from "react-router-dom";
import { AlertModal } from "../../../components/AlertModal";
import { api } from "../../../service/api";

export function NewStaff() {
	const [isCpfSelected, setIsCpfSelected] = useState(true);
	const [staffName, setStaffName] = useState("");
	const [staffIdentification, setStaffIdentification] = useState("");
	const [staffUsername, setStaffUsername] = useState("");
	const [staffContact, setStaffContact] = useState("");
	const [staffPassword, setStaffPassword] = useState("");
	const [staffComission, setStaffComission] = useState("");
	const [staffPermission, setStaffPermission] = useState("staff");
	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	const navigate = useNavigate();

	const cpfMask = [
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];

	const cnpjMask = [
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"/",
		/\d/,
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];

	const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
		setStaffPermission(e.target.value);
	};

	async function handleNewStaff() {
		if (!staffName) {
			setAlertMessage("O nome do funcionário está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!staffIdentification) {
			setAlertMessage(
				isCpfSelected
					? "O CPF do funcionário está faltando..."
					: "O CNPJ do funcionário está faltando...",
			);
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		// Validação específica para CPF e CNPJ
		const cpfLength = 11;
		const cnpjLength = 14;
		const cleanIdentification = staffIdentification.replace(/\D/g, ""); // Remove caracteres não numéricos

		if (isCpfSelected && cleanIdentification.length !== cpfLength) {
			setAlertMessage("O CPF deve conter exatamente 11 números.");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!isCpfSelected && cleanIdentification.length !== cnpjLength) {
			setAlertMessage("O CNPJ deve conter exatamente 14 números.");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!staffUsername) {
			setAlertMessage("O nome de usuário está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!staffPassword) {
			setAlertMessage("A senha está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!staffContact) {
			setAlertMessage("O contato do funcionário está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (staffPermission === "staff" && !staffComission) {
			setAlertMessage("O percentual de comissão está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		try {
			await api.post("/users", {
				username: staffUsername,
				password: staffPassword,
				userType: staffPermission,
				fullname: staffName,
				cpf: isCpfSelected ? cleanIdentification : null,
				cnpj: !isCpfSelected ? cleanIdentification : null,
				personType: isCpfSelected ? "individual" : "legal",
				contact: staffContact,
				comissionRate: staffPermission === "staff" ? Number(staffComission) : 0,
			});

			setAlertMessage("Funcionário cadastrado com sucesso!");
			setAlertType("success");
			setAlertCallback(() => () => navigate("/lista-funcionarios"));
			setIsAlertModalOpen(true);
		} catch (error) {
			setAlertMessage(
				error.response?.data?.error || "Erro ao cadastrar funcionário.",
			);
			setAlertType("error");
			setIsAlertModalOpen(true);
		}
	}

	return (
		<div className="min-h-screen flex flex-col">
			<Header pagename={"Cadastrar Funcionário"} href={"/"} $logout={false} />

			<main className="flex-grow flex flex-col items-center py-8 px-4">
				<form
					id="cad-funcionario"
					className="text-base"
					onSubmit={(e) => {
						e.preventDefault();
						handleNewStaff();
					}}
				>
					<fieldset className="p-4 border border-gray-300 rounded-md flex flex-col gap-4 w-md">
						<div className="input-wrapper flex flex-col">
							<label htmlFor="nomefuncionario-input" className="mb-1">
								Nome do Funcionário:
							</label>
							<input
								type="text"
								id="nomefuncionario-input"
								className="text-sm border border-gray-300 rounded px-2 py-1"
								placeholder="Ex: Júnior Santos"
								value={staffName}
								onChange={(e) => setStaffName(e.target.value)}
							/>
						</div>

						<div className="flex flex-col">
							{/* Seleção entre CPF e CNPJ */}
							<div className="input-wrapper flex items-center space-x-4 mb-1.5">
								<div className="check flex items-center gap-1">
									<input
										type="radio"
										name="identification"
										id="cpfcheck"
										checked={isCpfSelected}
										onChange={() => setIsCpfSelected(true)}
										className="form-radio w-5 h-5"
									/>
									<label htmlFor="cpfcheck" className="text-lg">
										CPF:
									</label>
								</div>
								<div className="check flex items-center gap-1">
									<input
										type="radio"
										name="identification"
										id="cnpjcheck"
										checked={!isCpfSelected}
										onChange={() => setIsCpfSelected(false)}
										className="form-radio w-5 h-5"
									/>
									<label htmlFor="cnpjcheck" className="text-lg">
										CNPJ:
									</label>
								</div>
							</div>
							{isCpfSelected ? (
								<div className="input-wrapper">
									<label htmlFor="cpfnum" className="block text-lg">
										CPF:
									</label>
									<MaskedInput
										mask={cpfMask}
										id="cpfnum"
										placeholder="___.___.___-__"
										className="w-full text-sm border border-gray-300 rounded px-2 py-1"
										value={staffIdentification}
										onChange={(e) => setStaffIdentification(e.target.value)}
									/>
								</div>
							) : (
								<div className="input-wrapper">
									<label htmlFor="cnpjnum" className="block text-lg">
										CNPJ:
									</label>
									<MaskedInput
										mask={cnpjMask}
										id="cnpjnum"
										placeholder="__.___.___/____-__"
										className="w-full text-sm border border-gray-300 rounded px-2 py-1"
										value={staffIdentification}
										onChange={(e) => setStaffIdentification(e.target.value)}
									/>
								</div>
							)}
						</div>

						<div className="input-wrapper flex flex-col">
							<label htmlFor="username-input" className="mb-1">
								Nome de usuário: (Será usado para fazer login)
							</label>
							<input
								type="text"
								onKeyDown={(e) => {
									if (e.key === " ") {
										e.preventDefault();
									}
								}}
								id="username-input"
								className="text-sm border border-gray-300 rounded px-2 py-1"
								placeholder="Ex: juniorsts (sem espaços)"
								value={staffUsername}
								onChange={(e) => setStaffUsername(e.target.value)}
							/>
						</div>

						<div className="input-wrapper flex flex-col">
							<label htmlFor="senha-input" className="mb-1">
								Senha:
							</label>
							<input
								type="text"
								id="senha-input"
								className="text-sm border border-gray-300 rounded px-2 py-1"
								value={staffPassword}
								placeholder="********"
								onChange={(e) => setStaffPassword(e.target.value)}
							/>
						</div>

						<div className="input-wrapper flex flex-col">
							<label htmlFor="email-input" className="mb-1">
								Contato (Número ou E-mail):
							</label>
							<input
								type="text"
								id="email-input"
								placeholder="(XX) XXXX-XXXX / <EMAIL>"
								className="text-sm border border-gray-300 rounded px-2 py-1"
								value={staffContact}
								onChange={(e) => setStaffContact(e.target.value)}
							/>
						</div>

						{staffPermission === "staff" && (
							<div className="line-wrapper flex items-center justify-between gap-4">
								<label htmlFor="percentual-input" className="mb-1">
									<span>Percentual de comissão:</span>
								</label>
								<div className="input-wrapper">
									<input
										type="number"
										id="percentual-input"
										className="text-sm border border-gray-300 rounded px-2 py-1"
										placeholder="Ex: 10"
										value={staffComission}
										onChange={(e) => setStaffComission(e.target.value)}
									/>
									%
								</div>
							</div>
						)}

						<div className="input-wrapper flex flex-col">
							<label htmlFor="permission-input" className="mb-1">
								Permissão:
							</label>
							<select
								name="select"
								id="permission-input"
								className="text-sm border border-gray-300 rounded px-2 py-1"
								value={staffPermission}
								onChange={handleSelectChange}
							>
								<option value="staff">Staff</option>
								<option value="admin">Administrador</option>
							</select>
						</div>

						<button
							type="submit"
							className="bg-blue-300 border border-gray-300 rounded px-4 py-2 mt-2"
						>
							Confirmar adição
						</button>
					</fieldset>
				</form>
			</main>

			<Footer />
			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => {
					setIsAlertModalOpen(false);
					if (alertType === "success" && alertCallback) {
						alertCallback();
					}
				}}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</div>
	);
}
