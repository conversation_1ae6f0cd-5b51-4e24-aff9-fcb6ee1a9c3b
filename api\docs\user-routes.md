# User API Routes

## Base URL
- `/users` - <PERSON><PERSON> as rotas seguem o padrão RESTful

## Endpoints

### 1. GET /users
**Descrição:** Busca todos os usuários ativos

**Resposta de Sucesso (200):**
```json
[
  {
    "id": 1,
    "username": "admin",
    "userType": "admin",
    "is_active": true,
    "person": {
      "id": 1,
      "fullname": "Administrador Sistema",
      "contact": "(11) 99999-9999",
      "cpf": "123.456.789-00",
      "cnpj": null,
      "personType": "individual"
    },
    "staff": {
      "id": 1,
      "comissionRate": "5.00"
    }
  }
]
```

### 2. GET /users/:id
**Descrição:** Busca um usuário específico por ID

**Parâmetros:**
- `id` (number) - ID do usuário

**Resposta de Sucesso (200):**
```json
{
  "id": 1,
  "username": "admin",
  "userType": "admin",
  "is_active": true,
  "person": {
    "id": 1,
    "fullname": "Administra<PERSON> Siste<PERSON>",
    "contact": "(11) 99999-9999",
    "cpf": "123.456.789-00",
    "cnpj": null,
    "personType": "individual"
  },
  "staff": {
    "id": 1,
    "comissionRate": "5.00"
  }
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Usuário não encontrado!"
}
```

### 3. POST /users
**Descrição:** Cria um novo usuário

**Body:**
```json
{
  "fullname": "João Silva",
  "contact": "(11) 98765-4321",
  "cpf": "987.654.321-00",
  "cnpj": null,
  "personType": "individual",
  "username": "joao.silva",
  "password": "senha123",
  "userType": "staff",
  "comissionRate": 3.5
}
```

**Resposta de Sucesso (201):**
```json
{
  "message": "Usuário criado com sucesso!"
}
```

**Resposta de Erro (400):**
```json
{
  "error": "Há campo(s) não preenchido(s)."
}
```

**Resposta de Erro (409):**
```json
{
  "error": "CPF/CNPJ já cadastrado no sistema anteriormente"
}
```

### 4. POST /users/login
**Descrição:** Realiza login do usuário

**Body:**
```json
{
  "username": "admin",
  "password": "senha123"
}
```

**Resposta de Sucesso (200):**
```json
{
  "id": 1,
  "username": "admin",
  "userType": "admin",
  "is_active": true,
  "person": {
    "id": 1,
    "fullname": "Administrador Sistema",
    "contact": "(11) 99999-9999",
    "cpf": "123.456.789-00",
    "cnpj": null,
    "personType": "individual"
  }
}
```

**Resposta de Erro (400):**
```json
{
  "error": "Username e password são obrigatórios!"
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Nome de usuário ou senha incorreto(s)!"
}
```

### 5. PATCH /users/:id
**Descrição:** Atualiza um usuário existente

**Parâmetros:**
- `id` (number) - ID do usuário

**Body (campos opcionais):**
```json
{
  "fullname": "João Silva Santos",
  "contact": "(11) 98765-4321",
  "username": "joao.santos",
  "password": "novaSenha123",
  "userType": "admin",
  "comissionRate": 5.0
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Usuário atualizado com sucesso!"
}
```

### 6. DELETE /users/:id
**Descrição:** Remove um usuário (soft delete - marca como inativo)

**Parâmetros:**
- `id` (number) - ID do usuário

**Resposta de Sucesso (200):**
```json
{
  "message": "Usuário removido com sucesso!"
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Usuário não encontrado!"
}
```

## Códigos de Erro Comuns

- **400 Bad Request:** Dados inválidos ou campos obrigatórios não preenchidos
- **404 Not Found:** Usuário não encontrado
- **409 Conflict:** CPF/CNPJ já cadastrado
- **500 Internal Server Error:** Erro interno do servidor

## Validações

### Campos Obrigatórios (POST):
- `fullname` (string)
- `contact` (string)
- `cpf` OU `cnpj` (string) - pelo menos um deve ser fornecido
- `personType` (enum: "individual" | "legal")
- `username` (string)
- `password` (string)
- `userType` (enum: "staff" | "admin")

### Campos Opcionais (POST):
- `comissionRate` (number) - apenas para userType "staff"

### Campos Opcionais (PATCH):
- Todos os campos são opcionais
- Valores devem seguir as mesmas regras de validação quando fornecidos

### Regras de Negócio:
- CPF deve ser fornecido para personType "individual"
- CNPJ deve ser fornecido para personType "legal"
- Username deve ser único no sistema
- CPF/CNPJ devem ser únicos no sistema
