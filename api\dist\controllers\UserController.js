"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const personService_1 = require("../services/personService");
const userService_1 = require("../services/userService");
class UserController {
    getAll(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const allUsers = yield userService_1.UserService.getAllUsers();
                res.status(200).json(allUsers);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: "Internal Server Error" });
            }
        });
    }
    getById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const userId = parseInt(id, 10);
            if (isNaN(userId)) {
                return res.status(400).json({ error: "ID de usuário inválido!" });
            }
            try {
                const user = yield userService_1.UserService.getUserById(userId);
                if (!user) {
                    return res.status(404).json({ error: "Usuário não encontrado!" });
                }
                res.status(200).json(user);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: "Internal Server Error" });
            }
        });
    }
    create(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { fullname, contact, cpf, cnpj, personType, username, password, userType, comissionRate, } = req.body;
            try {
                // Validation functions
                const isAllDataOk = () => {
                    if (!fullname ||
                        !contact ||
                        !personType ||
                        !username ||
                        !password ||
                        !userType) {
                        return false;
                    }
                    if (!cpf && !cnpj) {
                        return false;
                    }
                    return true;
                };
                const isPersonTypeOk = () => {
                    return personType === "individual" || personType === "legal";
                };
                const isUserTypeOk = () => {
                    return userType === "staff" || userType === "admin";
                };
                // Validations
                if (!isAllDataOk()) {
                    return res
                        .status(400)
                        .json({ error: "Há campo(s) não preenchido(s)." });
                }
                if (!isPersonTypeOk()) {
                    return res
                        .status(400)
                        .json({ error: "Tipo de pessoa (jurídica ou física) incorreto." });
                }
                if (!isUserTypeOk()) {
                    return res.status(400).json({ error: "Tipo de usuário incorreto." });
                }
                // Check if username already exists
                const usernameAlreadyInUse = yield userService_1.UserService.checkUsername(username);
                if (usernameAlreadyInUse) {
                    return res.status(400).json({ error: "Nome de usuário já em uso." });
                }
                // Check if CPF/CNPJ already exists
                const cpfExists = cpf ? yield personService_1.PersonService.checkCPForCNPJ("cpf", cpf) : false;
                const cnpjExists = cnpj ? yield personService_1.PersonService.checkCPForCNPJ("cnpj", cnpj) : false;
                if (cpfExists || cnpjExists) {
                    return res.status(409).json({
                        error: "CPF/CNPJ já cadastrado no sistema anteriormente",
                    });
                }
                // Create person
                const person_id = yield personService_1.PersonService.registerPerson({
                    fullname,
                    contact,
                    cpf,
                    cnpj,
                    personType: personType,
                });
                // Create user
                yield userService_1.UserService.registerUser({
                    username,
                    password,
                    userType,
                    person_id,
                    comissionRate,
                });
                return res.status(201).json({ message: "Usuário criado com sucesso!" });
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: "Erro interno do servidor" });
            }
        });
    }
    patch(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const userId = Number.parseInt(id, 10);
            if (isNaN(userId)) {
                return res.status(400).json({ message: "ID de usuário inválido!" });
            }
            const { fullname, contact, cpf, cnpj, personType, username, password, userType, comissionRate, } = req.body;
            try {
                // Get current user
                const currentUser = yield userService_1.UserService.getUserById(userId);
                if (!currentUser) {
                    return res.status(404).json({ message: "Usuário não encontrado!" });
                }
                // Prepare update data
                const updateData = {};
                if (username !== undefined)
                    updateData.username = username;
                if (password !== undefined)
                    updateData.password = password;
                if (userType !== undefined)
                    updateData.userType = userType;
                if (comissionRate !== undefined)
                    updateData.comissionRate = comissionRate;
                // Update person data if provided
                if (fullname !== undefined || contact !== undefined || cpf !== undefined ||
                    cnpj !== undefined || personType !== undefined) {
                    const personUpdateData = {};
                    if (fullname !== undefined)
                        personUpdateData.fullname = fullname;
                    if (contact !== undefined)
                        personUpdateData.contact = contact;
                    if (cpf !== undefined)
                        personUpdateData.cpf = cpf;
                    if (cnpj !== undefined)
                        personUpdateData.cnpj = cnpj;
                    if (personType !== undefined)
                        personUpdateData.personType = personType;
                    const updatedPerson = yield personService_1.PersonService.updatePerson(currentUser.person.id, personUpdateData);
                    if (updatedPerson) {
                        updateData.person = updatedPerson;
                    }
                }
                // Check if there's anything to update
                if (Object.keys(updateData).length === 0) {
                    return res.status(400).json({ message: "Nenhum campo para atualizar." });
                }
                // Update user
                const updatedUser = yield userService_1.UserService.updateUser(userId, updateData);
                if (updatedUser) {
                    return res.status(200).json({ message: "Usuário atualizado com sucesso!" });
                }
                else {
                    return res.status(404).json({ message: "Usuário não atualizado." });
                }
            }
            catch (error) {
                console.error("Erro ao atualizar usuário:", error);
                return res.status(500).json({ message: "Erro ao atualizar usuário!" });
            }
        });
    }
    delete(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const userId = parseInt(id, 10);
            if (isNaN(userId)) {
                return res.status(400).json({ message: "ID de usuário inválido!" });
            }
            try {
                const result = yield userService_1.UserService.deleteUser(userId);
                if (result) {
                    return res
                        .status(200)
                        .json({ message: "Usuário deletado com sucesso!." });
                }
                else {
                    return res.status(404).json({ message: "Usuário não encontrado." });
                }
            }
            catch (error) {
                console.error("Erro ao deletar usuário:", error);
                return res.status(500).json({ message: "Internal server error." });
            }
        });
    }
    login(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username, password } = req.body;
            if (!username || !password) {
                return res.status(400).json({ error: "Username e password são obrigatórios!" });
            }
            try {
                const user = yield userService_1.UserService.login(username, password);
                if (!user) {
                    return res
                        .status(404)
                        .json({ error: "Nome de usuário ou senha incorreto(s)!" });
                }
                return res.status(200).json(user);
            }
            catch (error) {
                console.error("Erro no login:", error);
                return res.status(500).json({ error: "Erro interno do servidor" });
            }
        });
    }
}
exports.default = new UserController();
