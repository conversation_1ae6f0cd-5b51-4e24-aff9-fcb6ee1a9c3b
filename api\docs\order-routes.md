# Order API Routes

## Base URL
- `/orders` - <PERSON><PERSON> as rotas seguem o padrão RESTful

## Endpoints

### 1. GET /orders
**Descrição:** Busca todos os pedidos

**Resposta de Sucesso (200):**
```json
[
  {
    "id": 1,
    "date": "2024-12-19",
    "time": "09:00:00",
    "deadline": "2024-12-22",
    "status": "Fila de Espera",
    "customer": {
      "id": 1,
      "person": {
        "fullname": "<PERSON>",
        "contact": "(11) 98765-4321"
      },
      "address": "Rua das Flores, 123"
    },
    "user": {
      "id": 1,
      "username": "admin",
      "person": {
        "fullname": "Administrador Sistema"
      }
    },
    "garments": [
      {
        "id": 1,
        "refcode": "102050",
        "name": "Camiseta Branca",
        "quantity": 5,
        "price": 25.00
      }
    ]
  }
]
```

**Resposta de <PERSON>rro (404):**
```json
{
  "message": "Nenhum pedido encontrado!"
}
```

### 2. GET /orders/:id
**Descrição:** Busca um pedido específico por ID

**Parâmetros:**
- `id` (number) - ID do pedido

**Resposta de Sucesso (200):**
```json
{
  "id": 1,
  "date": "2024-12-19",
  "time": "09:00:00",
  "deadline": "2024-12-22",
  "status": "Fila de Espera",
  "customer": {
    "id": 1,
    "person": {
      "fullname": "Maria Silva",
      "contact": "(11) 98765-4321"
    },
    "address": "Rua das Flores, 123"
  },
  "user": {
    "id": 1,
    "username": "admin",
    "person": {
      "fullname": "Administrador Sistema"
    }
  },
  "garments": [
    {
      "id": 1,
      "refcode": "102050",
      "name": "Camiseta Branca",
      "quantity": 5,
      "price": 25.00
    }
  ]
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Pedido não encontrado"
}
```

### 3. POST /orders
**Descrição:** Cria um novo pedido

**Body:**
```json
{
  "customer_id": 1,
  "staff_id": 1,
  "deadline": "2024-12-25",
  "status": "Fila de Espera",
  "garments": [
    {
      "id": 1,
      "quantity": 5
    },
    {
      "id": 2,
      "quantity": 3
    }
  ]
}
```

**Resposta de Sucesso (201):**
```json
{
  "message": "Pedido criado com sucesso!"
}
```

**Resposta de Erro (400):**
```json
{
  "message": "Cliente não encontrado"
}
```

**Resposta de Erro (400):**
```json
{
  "message": "Funcionário não encontrado"
}
```

**Resposta de Erro (400):**
```json
{
  "message": "É necessário incluir pelo menos um modelo no pedido"
}
```

### 4. PATCH /orders/:id
**Descrição:** Atualiza um pedido existente

**Parâmetros:**
- `id` (number) - ID do pedido

**Body (campos opcionais):**
```json
{
  "deadline": "2024-12-30",
  "status": "Em Produção",
  "customer_id": 2,
  "staff_id": 3
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Pedido atualizado com sucesso!",
  "order": {
    "id": 1,
    "date": "2024-12-19",
    "time": "09:00:00",
    "deadline": "2024-12-30",
    "status": "Em Produção",
    "customer_id": 2,
    "staff_id": 3
  }
}
```

### 5. DELETE /orders/:id
**Descrição:** Remove um pedido

**Parâmetros:**
- `id` (number) - ID do pedido

**Resposta de Sucesso (200):**
```json
{
  "message": "Pedido removido com sucesso!"
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Pedido não encontrado"
}
```

## Códigos de Erro Comuns

- **400 Bad Request:** Dados inválidos ou campos obrigatórios não preenchidos
- **404 Not Found:** Pedido, cliente ou funcionário não encontrado
- **500 Internal Server Error:** Erro interno do servidor

## Validações

### Campos Obrigatórios (POST):
- `customer_id` (number) - ID do cliente (deve existir)
- `staff_id` (number) - ID do funcionário (deve existir)
- `deadline` (string) - Data limite no formato "YYYY-MM-DD"
- `status` (string) - Status do pedido
- `garments` (array) - Lista de modelos com quantidades

### Estrutura do array garments:
```json
{
  "id": 1,        // ID do modelo (obrigatório)
  "quantity": 5   // Quantidade (obrigatório, > 0)
}
```

### Campos Opcionais (PATCH):
- `deadline` (string) - Nova data limite
- `status` (string) - Novo status
- `customer_id` (number) - Novo cliente
- `staff_id` (number) - Novo funcionário

### Status Válidos:
- "Fila de Espera"
- "Em Produção"
- "Pronto pra Entrega"
- "Concluído"
- "Cancelado"

### Fluxo de Status:
1. **Fila de Espera** → **Em Produção**: Pedido entra em produção
2. **Em Produção** → **Pronto pra Entrega**: Produção finalizada, aguardando entrega
3. **Pronto pra Entrega** → **Concluído**: Pedido entregue ao cliente (reduz estoque)
4. **Pronto pra Entrega** → **Fila de Espera**: Cancelar produção (volta ao início)
5. **Qualquer Status** → **Cancelado**: Cancelamento do pedido

### Regras de Negócio:
- Cliente deve existir e estar ativo
- Funcionário deve existir e estar ativo
- Deve incluir pelo menos um modelo no pedido
- Quantidade de cada modelo deve ser maior que zero
- Data e hora são geradas automaticamente na criação
