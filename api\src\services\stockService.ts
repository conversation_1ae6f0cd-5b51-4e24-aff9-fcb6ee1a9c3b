import { AppDataSource } from '../data-source';
import { GarmentService } from './garmentService';

interface StockOperation {
    garmentId: number;
    quantity: number;
    operation: 'add' | 'subtract';
}

interface StockValidationResult {
    valid: boolean;
    errors: string[];
    insufficientItems: Array<{
        garmentId: number;
        required: number;
        available: number;
        name?: string;
        refcode?: string;
    }>;
}

export class StockService {

    /**
     * Valida se múltiplas operações de estoque podem ser realizadas
     */
    public static async validateStockOperations(operations: StockOperation[]): Promise<StockValidationResult> {
        const errors: string[] = [];
        const insufficientItems: Array<{
            garmentId: number;
            required: number;
            available: number;
            name?: string;
            refcode?: string;
        }> = [];

        try {
            for (const operation of operations) {
                if (operation.operation === 'subtract') {
                    const stockCheck = await GarmentService.checkStockAvailability(
                        operation.garmentId, 
                        operation.quantity
                    );

                    if (!stockCheck.available) {
                        const garment = await GarmentService.getGarmentById(operation.garmentId);
                        errors.push(stockCheck.message || 'Estoque insuficiente');
                        insufficientItems.push({
                            garmentId: operation.garmentId,
                            required: operation.quantity,
                            available: stockCheck.currentStock,
                            name: garment?.name,
                            refcode: garment?.refcode
                        });
                    }
                }
            }

            return {
                valid: errors.length === 0,
                errors,
                insufficientItems
            };
        } catch (error) {
            console.error('Error validating stock operations:', error);
            return {
                valid: false,
                errors: ['Erro interno ao validar operações de estoque'],
                insufficientItems: []
            };
        }
    }

    /**
     * Executa múltiplas operações de estoque em uma transação
     */
    public static async executeStockOperations(operations: StockOperation[]): Promise<boolean> {
        const queryRunner = AppDataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            // Primeiro valida todas as operações
            const validation = await this.validateStockOperations(operations);
            if (!validation.valid) {
                throw new Error(`Operações de estoque inválidas: ${validation.errors.join(', ')}`);
            }

            // Executa todas as operações dentro da transação
            for (const operation of operations) {
                await GarmentService.updateStock(
                    operation.garmentId,
                    operation.quantity,
                    operation.operation
                );
            }

            await queryRunner.commitTransaction();
            return true;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            console.error('Error executing stock operations:', error);
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    /**
     * Processa estoque para conclusão de pedido (subtrai do estoque)
     */
    public static async processOrderCompletion(orderGarments: Array<{ id: number; quantity: number }>): Promise<StockValidationResult> {
        const operations: StockOperation[] = orderGarments.map(garment => ({
            garmentId: garment.id,
            quantity: garment.quantity,
            operation: 'subtract' as const
        }));

        const validation = await this.validateStockOperations(operations);
        
        if (validation.valid) {
            try {
                await this.executeStockOperations(operations);
            } catch (error) {
                return {
                    valid: false,
                    errors: [error instanceof Error ? error.message : 'Erro ao processar conclusão do pedido'],
                    insufficientItems: []
                };
            }
        }

        return validation;
    }

    /**
     * Processa estoque para produção de pedido (adiciona ao estoque)
     */
    public static async processOrderProduction(orderGarments: Array<{ id: number; quantity: number }>): Promise<boolean> {
        const operations: StockOperation[] = orderGarments.map(garment => ({
            garmentId: garment.id,
            quantity: garment.quantity,
            operation: 'add' as const
        }));

        try {
            await this.executeStockOperations(operations);
            return true;
        } catch (error) {
            console.error('Error processing order production:', error);
            throw error;
        }
    }

    /**
     * Obtém o status atual do estoque para múltiplas peças
     */
    public static async getStockStatus(garmentIds: number[]): Promise<Array<{
        garmentId: number;
        currentStock: number;
        name?: string;
        refcode?: string;
    }>> {
        try {
            const stockStatus = [];
            
            for (const garmentId of garmentIds) {
                const garment = await GarmentService.getGarmentById(garmentId);
                if (garment) {
                    stockStatus.push({
                        garmentId: garment.id,
                        currentStock: garment.in_stock,
                        name: garment.name,
                        refcode: garment.refcode
                    });
                }
            }

            return stockStatus;
        } catch (error) {
            console.error('Error getting stock status:', error);
            throw error;
        }
    }
}
