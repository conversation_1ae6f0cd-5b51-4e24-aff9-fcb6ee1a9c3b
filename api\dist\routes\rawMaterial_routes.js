"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const rawMaterialController_1 = __importDefault(require("../controllers/rawMaterialController"));
const rawMaterialRoutes = (0, express_1.Router)();
rawMaterialRoutes.get('/', rawMaterialController_1.default.getAll);
rawMaterialRoutes.get('/:id', rawMaterialController_1.default.getById);
rawMaterialRoutes.post('/', rawMaterialController_1.default.create);
rawMaterialRoutes.patch('/:id', rawMaterialController_1.default.patch);
rawMaterialRoutes.delete('/:id', rawMaterialController_1.default.delete);
exports.default = rawMaterialRoutes;
