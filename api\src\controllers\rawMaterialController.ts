import { Request, Response } from 'express';
import { RawMaterialService } from '../services/rawMaterialService';

class RawMaterialController {
    
    public async getAll(req: Request, res: Response) {
        try {
            const rawMaterials = await RawMaterialService.getAllRawMaterials();
            return res.status(200).json(rawMaterials);
        } catch (error) {
            console.error(error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async getById(req: Request, res: Response) {
        try {
            const rawMaterialId = Number.parseInt(req.params.id);
            
            if (Number.isNaN(rawMaterialId)) {
                return res.status(400).json({ error: 'ID inválido' });
            }

            const rawMaterial = await RawMaterialService.getRawMaterialById(rawMaterialId);
            
            if (!rawMaterial) {
                return res.status(404).json({ error: 'Matéria-prima não encontrada' });
            }

            return res.status(200).json(rawMaterial);
        } catch (error) {
            console.error(error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async create(req: Request, res: Response) {
        const { name, manufacturer, pricePerKg, color, weight } = req.body;
        
        // Mapear pricePerKg para price_per_weight para manter compatibilidade com o frontend
        const price_per_weight = pricePerKg;

        if (!await RawMaterialService.validateRawMaterialData(name, manufacturer, price_per_weight, color, weight)) {
            return res.status(400).json({ error: 'Há campo(s) não preenchido(s) ou com valores inválidos!' });
        }

        try {
            const savedRawMaterial = await RawMaterialService.createRawMaterial({
                name,
                manufacturer,
                price_per_weight,
                color,
                weight
            });

            return res.status(201).json({
                message: 'Matéria-prima registrada com sucesso!',
                rawMaterial: savedRawMaterial
            });
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async patch(req: Request, res: Response) {
        try {
            const rawMaterialId = Number.parseInt(req.params.id);
            
            if (Number.isNaN(rawMaterialId)) {
                return res.status(400).json({ error: 'ID inválido' });
            }

            const { name, manufacturer, pricePerKg, color, weight } = req.body;
            
            // Criar objeto de atualização apenas com campos fornecidos
            const updateData: any = {};
            if (name !== undefined) updateData.name = name;
            if (manufacturer !== undefined) updateData.manufacturer = manufacturer;
            if (pricePerKg !== undefined) updateData.price_per_weight = pricePerKg;
            if (color !== undefined) updateData.color = color;
            if (weight !== undefined) updateData.weight = weight;

            // Validar se pelo menos um campo foi fornecido
            if (Object.keys(updateData).length === 0) {
                return res.status(400).json({ error: 'Nenhum campo para atualizar foi fornecido' });
            }

            // Validar valores numéricos se fornecidos
            if (updateData.price_per_weight !== undefined && updateData.price_per_weight <= 0) {
                return res.status(400).json({ error: 'Preço por peso deve ser maior que zero' });
            }
            
            if (updateData.weight !== undefined && updateData.weight <= 0) {
                return res.status(400).json({ error: 'Peso deve ser maior que zero' });
            }

            const updatedRawMaterial = await RawMaterialService.updateRawMaterial(rawMaterialId, updateData);
            
            if (!updatedRawMaterial) {
                return res.status(404).json({ error: 'Matéria-prima não encontrada' });
            }

            return res.status(200).json({
                message: 'Matéria-prima atualizada com sucesso!',
                rawMaterial: updatedRawMaterial
            });
        } catch (error) {
            console.error(error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async delete(req: Request, res: Response) {
        try {
            const rawMaterialId = Number.parseInt(req.params.id);
            
            if (Number.isNaN(rawMaterialId)) {
                return res.status(400).json({ error: 'ID inválido' });
            }

            const deleted = await RawMaterialService.deleteRawMaterial(rawMaterialId);
            
            if (!deleted) {
                return res.status(404).json({ error: 'Matéria-prima não encontrada' });
            }

            return res.status(200).json({ message: 'Matéria-prima removida com sucesso!' });
        } catch (error) {
            console.error(error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    }
}

export default new RawMaterialController();
