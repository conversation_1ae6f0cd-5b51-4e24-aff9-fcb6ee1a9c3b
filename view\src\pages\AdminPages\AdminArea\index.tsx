import { FaLock } from "react-icons/fa";
import { <PERSON> } from "react-router-dom";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

interface User {
  id: number;
  username: string;
  userType: "staff" | "admin";
  is_active: boolean;
  person: {
    id: number;
    fullname: string;
    contact: string;
    cpf: string | null;
    cnpj: string | null;
    personType: "individual" | "legal";
  };
  staff?: {
    id: number;
    comissionRate: string;
  };
}

export function AdminArea() {
  let user: User | null = null;
  const storedUser = localStorage.getItem("@managermalhas:user");

  if (storedUser) {
    try {
      user = JSON.parse(storedUser) as User;
    } catch (error) {
      console.error("Erro ao fazer o parse do usuário:", error);
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header pagename="Área Administrativa" href="/home" $logout={false} />

      <main className="flex-grow text-center font-bold">
        <h1 className="mt-6 mb-8 text-2xl">Área Administrativa</h1>

        <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3 mb-3">
          {user?.userType === "staff" ? (
            <>
              <Link
                to="#"
                type="button"
                onClick={(e) => e.preventDefault()}
                className="bg-gray-300 text-gray-500 flex flex-col items-center py-20 px-0 text-lg mx-8 font-roboto pointer-events-none cursor-not-allowed"
                aria-disabled="true"
              >
                <FaLock aria-label="Cadeado, acesso bloqueado" className="w-6 h-6 mb-2" />
                <p>Cadastrar funcionário</p>
              </Link>
              <Link
                to="#"
                type="button"
                onClick={(e) => e.preventDefault()}
                className="bg-gray-300 text-gray-500 flex flex-col items-center py-20 px-0 text-lg mx-8 font-roboto pointer-events-none cursor-not-allowed"
                aria-disabled="true"
              >
                <FaLock aria-label="Cadeado, acesso bloqueado" className="w-6 h-6 mb-2" />
                <p>Listar Funcionários</p>
              </Link>
              <Link
                to="#"
                type="button"
                onClick={(e) => e.preventDefault()}
                className="bg-gray-300 text-gray-500 flex flex-col items-center py-20 px-0 text-lg mx-8 font-roboto pointer-events-none cursor-not-allowed"
                aria-disabled="true"
              >
                <FaLock aria-label="Cadeado, acesso bloqueado" className="w-6 h-6 mb-2" />
                <p>Lista de Vendas</p>
              </Link>
            </>
          ) : (
            <>
              <Link
                to="/novo-funcionario"
                type="button"
                className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
              >
                <p>Cadastrar funcionário</p>
              </Link>
              <Link
                to="/lista-de-funcionarios"
                type="button"
                className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
              >
                <p>Listar funcionários</p>
              </Link>
              <Link
                to="/lista-de-vendas"
                type="button"
                className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
              >
                <p>Lista de Vendas</p>
              </Link>
            </>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
} 