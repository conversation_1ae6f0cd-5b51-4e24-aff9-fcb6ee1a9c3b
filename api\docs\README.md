# API Documentation

Esta pasta contém a documentação completa de todas as rotas da API do sistema de gerenciamento de malharias.

## Estrutura da Documentação

Cada arquivo de documentação segue o padrão RESTful e contém:
- Base URL do módulo
- Lista completa de endpoints
- Exemplos de requisições e respostas
- Códigos de erro comuns
- Validações e regras de negócio

## Módulos Disponíveis

### 📁 [User Routes](./user-routes.md)
**Base URL:** `/users`

Gerenciamento de usuários do sistema (funcionários e administradores).

**Endpoints:**
- `GET /users` - Lista todos os usuários
- `GET /users/:id` - Busca usuário por ID
- `POST /users` - Cria novo usuário
- `POST /users/login` - Realiza login
- `PATCH /users/:id` - Atualiza usuário
- `DELETE /users/:id` - Remove usuário

---

### 👥 [Customer Routes](./customer-routes.md)
**Base URL:** `/customers`

Gerenciamento de clientes.

**Endpoints:**
- `GET /customers` - Lista todos os clientes
- `GET /customers/:id` - Busca cliente por ID
- `POST /customers` - Cria novo cliente
- `PATCH /customers/:id` - Atualiza cliente
- `DELETE /customers/:id` - Remove cliente

---

### 👕 [Garment Routes](./garment-routes.md)
**Base URL:** `/garments`

Gerenciamento de modelos/peças de roupa.

**Endpoints:**
- `GET /garments` - Lista todos os modelos
- `GET /garments/:id` - Busca modelo por ID
- `POST /garments` - Cria novo modelo
- `PATCH /garments/:id` - Atualiza modelo
- `DELETE /garments/:id` - Remove modelo

---

### 🧵 [Raw Material Routes](./raw-material-routes.md)
**Base URL:** `/raw-materials`

Gerenciamento de matérias-primas.

**Endpoints:**
- `GET /raw-materials` - Lista todas as matérias-primas
- `GET /raw-materials/:id` - Busca matéria-prima por ID
- `POST /raw-materials` - Cria nova matéria-prima
- `PATCH /raw-materials/:id` - Atualiza matéria-prima
- `DELETE /raw-materials/:id` - Remove matéria-prima

---

### 📋 [Order Routes](./order-routes.md)
**Base URL:** `/orders`

Gerenciamento de pedidos.

**Endpoints:**
- `GET /orders` - Lista todos os pedidos
- `GET /orders/:id` - Busca pedido por ID
- `POST /orders` - Cria novo pedido
- `PATCH /orders/:id` - Atualiza pedido
- `DELETE /orders/:id` - Remove pedido

---

## Padrões Gerais

### Autenticação
Atualmente a API não implementa autenticação via tokens. O login é realizado através do endpoint `/users/login`.

### Códigos de Status HTTP
- **200 OK** - Operação realizada com sucesso
- **201 Created** - Recurso criado com sucesso
- **400 Bad Request** - Dados inválidos ou campos obrigatórios não preenchidos
- **404 Not Found** - Recurso não encontrado
- **409 Conflict** - Conflito de dados (ex: CPF/CNPJ duplicado)
- **500 Internal Server Error** - Erro interno do servidor

### Formato de Respostas
Todas as respostas são retornadas em formato JSON.

#### Sucesso:
```json
{
  "message": "Operação realizada com sucesso!",
  "data": { /* dados do recurso */ }
}
```

#### Erro:
```json
{
  "error": "Descrição do erro"
}
```

### Soft Delete
A maioria dos recursos utiliza soft delete, ou seja, os registros não são removidos fisicamente do banco de dados, apenas marcados como inativos através do campo `is_active`.

### Validações Comuns
- Campos de texto não podem estar vazios
- Valores numéricos devem ser maiores que zero (quando aplicável)
- CPF/CNPJ devem ser únicos no sistema
- Códigos de referência devem ser únicos

## Como Usar Esta Documentação

1. Escolha o módulo que deseja consultar
2. Clique no link correspondente
3. Consulte os endpoints disponíveis
4. Verifique os exemplos de requisição e resposta
5. Observe as validações e regras de negócio específicas

## Contribuição

Para atualizar a documentação:
1. Edite o arquivo correspondente ao módulo
2. Mantenha o padrão estabelecido
3. Inclua exemplos práticos
4. Documente todas as validações e regras de negócio
