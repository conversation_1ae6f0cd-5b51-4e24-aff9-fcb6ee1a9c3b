"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const customerService_1 = require("../services/customerService");
class CustomerController {
    getAll(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const allCustomers = yield customerService_1.CustomerService.getAllCustomers();
                res.status(200).json(allCustomers);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error.' });
            }
        });
    }
    getById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const customerId = parseInt(id, 10);
            if (isNaN(customerId)) {
                return res.status(400).json({ message: 'ID do cliente inválido.' });
            }
            try {
                const customer = yield customerService_1.CustomerService.getCustomerById(customerId);
                return customer ? res.status(200).json(customer) : res.status(404).json({ message: 'Cliente não encontrado.' });
            }
            catch (error) {
                console.error('Error fetching customer:', error);
                return res.status(500).json({ message: 'Error fetching customer' });
            }
        });
    }
    create(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { fullname, contact, cpf, cnpj, personType, address } = req.body;
            try {
                const newCustomer = yield customerService_1.CustomerService.createCustomer({
                    fullname,
                    contact,
                    cpf,
                    cnpj,
                    personType,
                    address
                });
                res.status(201).json(newCustomer);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    patch(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const customerId = parseInt(id, 10);
            if (isNaN(customerId)) {
                return res.status(400).json({ message: 'ID de cliente inválido!' });
            }
            const { fullname, contact, cpf, cnpj, address } = req.body;
            if (!fullname && !contact && cpf === undefined && cnpj === undefined && !address) {
                return res.status(400).json({ message: 'Nenhum campo para atualizar.' });
            }
            try {
                const updated = yield customerService_1.CustomerService.updateCustomer(customerId, {
                    fullname,
                    contact,
                    cpf,
                    cnpj,
                    address
                });
                if (updated) {
                    return res.status(200).json({ message: 'Cliente atualizado com sucesso!' });
                }
                else {
                    return res.status(404).json({ message: 'Cliente não encontrado!' });
                }
            }
            catch (error) {
                console.error('Erro ao atualizar cliente:', error);
                return res.status(500).json({ message: 'Erro ao atualizar cliente!' });
            }
        });
    }
    delete(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const customerId = parseInt(id, 10);
            if (isNaN(customerId)) {
                return res.status(400).json({ message: 'ID de cliente inválido.' });
            }
            try {
                const deleted = yield customerService_1.CustomerService.deleteCustomer(customerId);
                if (deleted) {
                    return res.status(200).json({ message: 'Cliente deletado com sucesso!' });
                }
                else {
                    return res.status(404).json({ message: 'Registro de cliente não encontrado.' });
                }
            }
            catch (error) {
                console.error('Erro ao deletar cliente:', error);
                return res.status(500).json({ message: 'Erro ao deletar cliente!' });
            }
        });
    }
}
exports.default = new CustomerController();
