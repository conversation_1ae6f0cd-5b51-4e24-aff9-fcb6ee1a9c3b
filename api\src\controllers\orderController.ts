import type { Request, Response } from 'express';
import { OrderService } from '../services/orderService';

interface IGarmentIntoOrder {
    id: number;
    quantity: number;
}

class OrderController {
    public static async create(req: Request, res: Response) {
        try {
            const { customer_id, staff_id, deadline, status, garments }: {
                customer_id: number,
                staff_id: number,
                deadline: string,
                status: string,
                garments: IGarmentIntoOrder[]
            } = req.body;

            await OrderService.createOrderFromRequest({
                customer_id,
                staff_id,
                deadline,
                status,
                garments
            });

            return res.status(201).json({ message: 'Pedido criado com sucesso!' });
        } catch (error) {
            console.error(error);
            if (error instanceof Error) {
                if (error.message.includes('não encontrado') ||
                    error.message.includes('não foi definido') ||
                    error.message.includes('necessário incluir')) {
                    return res.status(400).json({ message: error.message });
                }
            }
            return res.status(500).json({ message: 'Internal server error' });
        }
    }

    public static async getAll(_req: Request, res: Response) {
        try {
            const orders = await OrderService.getAllOrdersFormatted();

            if (!orders || orders.length === 0) {
                return res.status(404).json({ message: 'Nenhum pedido encontrado!' });
            }

            return res.status(200).json(orders);
        } catch (error) {
            console.error('Error fetching orders:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

    public static async getById(req: Request, res: Response) {
        const { id } = req.params;
        const orderId = parseInt(id, 10);

        if (isNaN(orderId)) {
            return res.status(400).json({ message: 'ID de pedido inválido!' });
        }

        try {
            const order = await OrderService.getOrderById(orderId);
            if (!order) {
                return res.status(404).json({ message: 'Pedido não encontrado!' });
            }
            return res.status(200).json(order);
        } catch (error) {
            console.error('Error fetching order by ID:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

    public static async patch(req: Request, res: Response) {
        const { id } = req.params;
        const orderId = parseInt(id, 10);
        const updates = req.body;

        if (isNaN(orderId)) {
            return res.status(400).json({ message: 'ID de pedido inválido!' });
        }

        try {
            const existingOrder = await OrderService.getOrderById(orderId);
            if (!existingOrder) {
                return res.status(404).json({ error: 'Pedido não encontrado!' });
            }

            const updated = await OrderService.updateOrder(orderId, updates);

            if (!updated) {
                return res.status(500).json({ error: 'Falha ao atualizar pedido!' });
            }

            return res.status(200).json({ message: 'Pedido atualizado com sucesso!'});
        } catch (error) {
            console.error('Error updating order:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

    public static async delete(req: Request, res: Response) {
        const { id } = req.params;
        const orderId = parseInt(id, 10);

        if (isNaN(orderId)) {
            return res.status(400).json({ message: 'ID de pedido inválido!' });
        }

        try {
            const result = await OrderService.deleteOrder(orderId);

            if (result) {
                return res.status(200).json({ message: 'Pedido deletado com sucesso!' });
            }
                return res.status(404).json({ error: 'Pedido não encontrado!' });
        } catch (error) {
            console.error('Error deleting order:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }
}

export default OrderController;