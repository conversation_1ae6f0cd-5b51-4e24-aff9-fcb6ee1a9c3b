import { useState } from "react";
import { useNavigate } from "react-router-dom";
import logo from "../../assets/logo.png";
import { useAuth } from "../../hooks/auth";

export function Login() {
	const [username, setUsername] = useState("");
	const [password, setPassword] = useState("");

	const { signIn } = useAuth();

	const navigate = useNavigate();

	async function handleLogin() {
		try {
			await signIn({ username, password });
			navigate("/home");
		} catch (error: any) {
			console.error("Erro ao fazer o login:", error.response?.data || error.message);
		}
	}

	return (
		<>
			<div
				id="app"
				className="flex flex-col items-center justify-center h-screen bg-blue-300"
			>
				<div id="logo-box" className="mb-9">
					<img
						src={logo}
						alt="Logo da empresa"
						id="logo"
						className="w-60 rounded-lg"
					/>
				</div>

				<div
					id="login"
					className="flex flex-col items-center bg-white p-6 border border-black rounded-lg min-w-[300px] mx-8"
				>
					<h1 className="font-rhodium text-center text-2xl mb-5">Login</h1>

					<div id="input-area" className="flex flex-col gap-3 mb-4 w-full">
						<div className="input-wrapper flex flex-col gap-1 text-lg">
							<label htmlFor="userId" className="font-rhodium">
								Usuário
							</label>
							<input
								type="text"
								id="userId"
								placeholder="Usuário Administrador"
								className="p-1 border rounded-md text-base"
								onChange={(e) => setUsername(e.target.value)}
							/>
						</div>

						<div className="input-wrapper flex flex-col gap-1 text-lg">
							<label htmlFor="userPassword" className="font-rhodium">
								Senha
							</label>
							<input
								type="password"
								id="userPassword"
								placeholder="******"
								className="p-1 border rounded-md text-base"
								onChange={(e) => setPassword(e.target.value)}
							/>
						</div>
					</div>

					<button
						type="submit"
						onClick={handleLogin}
						id="loginBtn"
						className="font-sans text-base font-bold py-2 px-10 border border-black rounded-lg bg-blue-200"
					>
						Logar
					</button>
				</div>
			</div>
		</>
	);
}
