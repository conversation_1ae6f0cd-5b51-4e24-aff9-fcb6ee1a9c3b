"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const rawMaterialService_1 = require("../services/rawMaterialService");
class RawMaterialController {
    getAll(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterials = yield rawMaterialService_1.RawMaterialService.getAllRawMaterials();
                return res.status(200).json(rawMaterials);
            }
            catch (error) {
                console.error(error);
                return res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    getById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterialId = Number.parseInt(req.params.id);
                if (Number.isNaN(rawMaterialId)) {
                    return res.status(400).json({ error: 'ID inválido' });
                }
                const rawMaterial = yield rawMaterialService_1.RawMaterialService.getRawMaterialById(rawMaterialId);
                if (!rawMaterial) {
                    return res.status(404).json({ error: 'Matéria-prima não encontrada' });
                }
                return res.status(200).json(rawMaterial);
            }
            catch (error) {
                console.error(error);
                return res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    create(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { name, manufacturer, pricePerKg, color, weight } = req.body;
            // Mapear pricePerKg para price_per_weight para manter compatibilidade com o frontend
            const price_per_weight = pricePerKg;
            if (!(yield rawMaterialService_1.RawMaterialService.validateRawMaterialData(name, manufacturer, price_per_weight, color, weight))) {
                return res.status(400).json({ error: 'Há campo(s) não preenchido(s) ou com valores inválidos!' });
            }
            try {
                const savedRawMaterial = yield rawMaterialService_1.RawMaterialService.createRawMaterial({
                    name,
                    manufacturer,
                    price_per_weight,
                    color,
                    weight
                });
                return res.status(201).json({
                    message: 'Matéria-prima registrada com sucesso!',
                    rawMaterial: savedRawMaterial
                });
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    patch(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterialId = Number.parseInt(req.params.id);
                if (Number.isNaN(rawMaterialId)) {
                    return res.status(400).json({ error: 'ID inválido' });
                }
                const { name, manufacturer, pricePerKg, color, weight } = req.body;
                // Criar objeto de atualização apenas com campos fornecidos
                const updateData = {};
                if (name !== undefined)
                    updateData.name = name;
                if (manufacturer !== undefined)
                    updateData.manufacturer = manufacturer;
                if (pricePerKg !== undefined)
                    updateData.price_per_weight = pricePerKg;
                if (color !== undefined)
                    updateData.color = color;
                if (weight !== undefined)
                    updateData.weight = weight;
                // Validar se pelo menos um campo foi fornecido
                if (Object.keys(updateData).length === 0) {
                    return res.status(400).json({ error: 'Nenhum campo para atualizar foi fornecido' });
                }
                // Validar valores numéricos se fornecidos
                if (updateData.price_per_weight !== undefined && updateData.price_per_weight <= 0) {
                    return res.status(400).json({ error: 'Preço por peso deve ser maior que zero' });
                }
                if (updateData.weight !== undefined && updateData.weight <= 0) {
                    return res.status(400).json({ error: 'Peso deve ser maior que zero' });
                }
                const updatedRawMaterial = yield rawMaterialService_1.RawMaterialService.updateRawMaterial(rawMaterialId, updateData);
                if (!updatedRawMaterial) {
                    return res.status(404).json({ error: 'Matéria-prima não encontrada' });
                }
                return res.status(200).json({
                    message: 'Matéria-prima atualizada com sucesso!',
                    rawMaterial: updatedRawMaterial
                });
            }
            catch (error) {
                console.error(error);
                return res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    delete(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterialId = Number.parseInt(req.params.id);
                if (Number.isNaN(rawMaterialId)) {
                    return res.status(400).json({ error: 'ID inválido' });
                }
                const deleted = yield rawMaterialService_1.RawMaterialService.deleteRawMaterial(rawMaterialId);
                if (!deleted) {
                    return res.status(404).json({ error: 'Matéria-prima não encontrada' });
                }
                return res.status(200).json({ message: 'Matéria-prima removida com sucesso!' });
            }
            catch (error) {
                console.error(error);
                return res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
}
exports.default = new RawMaterialController();
