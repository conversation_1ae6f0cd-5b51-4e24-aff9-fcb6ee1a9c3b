# Stock API Routes

## Base URL
- `/stock` - Todas as rotas seguem o padrão RESTful para operações de estoque

## Endpoints

### 1. PATCH /stock/update
**Descrição:** Atualiza o estoque de uma única peça

**Body:**
```json
{
  "garmentId": 1,
  "quantity": 10,
  "operation": "add" // "add", "subtract", ou "set"
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Estoque atualizado com sucesso!"
}
```

**Resposta de Erro (400):**
```json
{
  "error": "Estoque insuficiente! Estoque atual: 5, tentativa de retirar: 10"
}
```

### 2. PATCH /stock/batch-update
**Descrição:** Atualiza o estoque de múltiplas peças em uma transação

**Body:**
```json
{
  "operations": [
    {
      "garmentId": 1,
      "quantity": 5,
      "operation": "add"
    },
    {
      "garmentId": 2,
      "quantity": 3,
      "operation": "subtract"
    }
  ]
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Operações de estoque executadas com sucesso!"
}
```

### 3. POST /stock/validate-order
**Descrição:** Valida se há estoque suficiente para completar um pedido

**Body:**
```json
{
  "garments": [
    {
      "id": 1,
      "quantity": 5
    },
    {
      "id": 2,
      "quantity": 3
    }
  ]
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Estoque suficiente para o pedido",
  "valid": true
}
```

**Resposta de Erro (400):**
```json
{
  "error": "Estoque insuficiente",
  "valid": false,
  "errors": ["Estoque insuficiente para peça ID 1"],
  "insufficientItems": [
    {
      "garmentId": 1,
      "required": 5,
      "available": 2,
      "name": "Camiseta Branca",
      "refcode": "102050"
    }
  ]
}
```

### 4. POST /stock/status
**Descrição:** Obtém o status atual do estoque para múltiplas peças

**Body:**
```json
{
  "garmentIds": [1, 2, 3]
}
```

**Resposta de Sucesso (200):**
```json
[
  {
    "garmentId": 1,
    "currentStock": 15,
    "name": "Camiseta Branca",
    "refcode": "102050"
  },
  {
    "garmentId": 2,
    "currentStock": 8,
    "name": "Calça Jeans",
    "refcode": "102051"
  }
]
```

## Operações Disponíveis

### Tipos de Operação
- **add**: Adiciona quantidade ao estoque atual
- **subtract**: Remove quantidade do estoque atual (com validação para evitar estoque negativo)
- **set**: Define o estoque para um valor específico

### Validações
- Estoque não pode ser negativo
- Operações em lote são executadas em transação (tudo ou nada)
- Validação de existência das peças antes da operação
- Tratamento de erros detalhado com mensagens específicas

### Casos de Uso
1. **Entrada de Mercadoria**: Use `add` para adicionar produtos recebidos
2. **Saída por Venda**: Use `subtract` para remover produtos vendidos
3. **Ajuste de Inventário**: Use `set` para corrigir discrepâncias
4. **Validação de Pedidos**: Use `validate-order` antes de processar pedidos
5. **Relatórios**: Use `status` para obter informações atuais do estoque
