import type { Request, Response } from "express";

import { ICreateCustomerDto, IPatchCustomerDto } from "../dto/CustomerDto";
import { customerRepository } from "../repositories/customerRepository";
import { ICreatePersonDto, IPatchPersonDto } from "../dto/PersonDto";
import { personRepository } from "../repositories/personRepository";
import { CustomerService } from "../services/customerService";


class CustomerController {
    public async getAll(req: Request, res: Response) {
        try {
            const allCustomers = await CustomerService.getAllCustomers();
            res.status(200).json(allCustomers);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error.' });
        }
    }

    public async getById (req: Request, res: Response) {
        const { id } = req.params;
        const customerId = parseInt(id, 10);

        if (isNaN(customerId)) {
            return res.status(400).json({ message: 'ID do cliente inválido.' });
        }

        try {
            const customer = await CustomerService.getCustomerById(customerId);
            return customer ? res.status(200).json(customer) : res.status(404).json({ message: 'Cliente não encontrado.' });
        } catch (error) {
            console.error('Error fetching customer:', error);
            return res.status(500).json({ message: 'Error fetching customer' });
        }
    }

    public async create(req: Request, res: Response) {
        const { fullname, contact, cpf, cnpj, personType, address } = req.body;

        try {
            const newCustomer = await CustomerService.createCustomer({
                fullname,
                contact,
                cpf,
                cnpj,
                personType,
                address
            });

            res.status(201).json(newCustomer);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async patch(req: Request, res: Response) {
        const { id } = req.params;
        const customerId = parseInt(id, 10);

        if (isNaN(customerId)) {
            return res.status(400).json({ message: 'ID de cliente inválido!' });
        }

        const { fullname, contact, cpf, cnpj, address } = req.body;

        if (!fullname && !contact && cpf === undefined && cnpj === undefined && !address) {
            return res.status(400).json({ message: 'Nenhum campo para atualizar.' });
        }

        try {
            const updated = await CustomerService.updateCustomer(customerId, {
                fullname,
                contact,
                cpf,
                cnpj,
                address
            });

            if (updated) {
                return res.status(200).json({ message: 'Cliente atualizado com sucesso!' });
            } else {
                return res.status(404).json({ message: 'Cliente não encontrado!' });
            }
        } catch (error) {
            console.error('Erro ao atualizar cliente:', error);
            return res.status(500).json({ message: 'Erro ao atualizar cliente!' });
        }
    }

    public async delete(req: Request, res: Response) {
        const {id} = req.params;
        const customerId = parseInt(id, 10);

        if (isNaN(customerId)) {
            return res.status(400).json({ message: 'ID de cliente inválido.' });
        }

        try {
            const deleted = await CustomerService.deleteCustomer(customerId);

            if (deleted) {
                return res.status(200).json({ message: 'Cliente deletado com sucesso!' });
            } else {
                return res.status(404).json({ message: 'Registro de cliente não encontrado.' });
            }
        } catch (error) {
            console.error('Erro ao deletar cliente:', error);
            return res.status(500).json({ message: 'Erro ao deletar cliente!' });
        }
    }
}

export default new CustomerController();
