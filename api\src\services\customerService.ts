import { ICreateCustomerDto, IPatchCustomerDto } from '../dto/CustomerDto';
import { ICreatePersonDto, IPatchPersonDto } from '../dto/PersonDto';
import { Customer } from '../entities/Customer';
import { Person } from '../entities/Person';
import { customerRepository } from '../repositories/customerRepository';
import { personRepository } from '../repositories/personRepository';

export class CustomerService {
    
    public static async getAllCustomers(): Promise<Customer[]> {
        try {
            const customers = await customerRepository.find({
                relations: {
                    person: true
                }
            });
            return customers;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async getCustomerById(customerId: number): Promise<Customer | null> {
        try {
            const customer = await customerRepository.findOne({
                where: { id: customerId },
                relations: {
                    person: true
                }
            });
            return customer || null;
        } catch (error) {
            console.error('Error fetching customer:', error);
            throw error;
        }
    }

    public static async createCustomer(customerData: {
        fullname: string;
        contact?: string;
        cpf?: string;
        cnpj?: string;
        personType: string;
        address: string;
    }): Promise<Customer> {
        try {
            const personData: ICreatePersonDto = {
                fullname: customerData.fullname,
                contact: customerData.contact,
                cpf: customerData.cpf,
                cnpj: customerData.cnpj,
                personType: customerData.personType as any
            };

            const newPerson = personRepository.create(personData);
            const savedPerson = await personRepository.save(newPerson);

            const newCustomerData: ICreateCustomerDto = {
                person: savedPerson,
                address: customerData.address,
                is_active: true
            };

            const newCustomer = customerRepository.create(newCustomerData);
            const savedCustomer = await customerRepository.save(newCustomer);

            return savedCustomer;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async updateCustomer(customerId: number, updateData: {
        fullname?: string;
        contact?: string;
        cpf?: string;
        cnpj?: string;
        address?: string;
    }): Promise<boolean> {
        try {
            const customer = await customerRepository.findOne({
                where: { id: customerId },
                relations: {
                    person: true
                }
            });

            if (!customer) {
                return false;
            }

            const personUpdatedData: IPatchPersonDto = {
                ...(updateData.fullname && { fullname: updateData.fullname }),
                ...(updateData.contact && { contact: updateData.contact }),
                ...(updateData.cpf !== undefined && { cpf: updateData.cpf }),
                ...(updateData.cnpj !== undefined && { cnpj: updateData.cnpj })
            };

            const customerUpdatedData: IPatchCustomerDto = {
                ...(updateData.address && { address: updateData.address })
            };

            const wasPersonUpdated = Object.keys(personUpdatedData).length > 0;
            const wasCustomerUpdated = Object.keys(customerUpdatedData).length > 0;

            if (!wasPersonUpdated && !wasCustomerUpdated) {
                return false;
            }

            const personId = customer.person.id;

            if (wasPersonUpdated) {
                await personRepository.update(personId, personUpdatedData);
            }

            if (wasCustomerUpdated) {
                await customerRepository.update(customerId, customerUpdatedData);
            }

            return true;
        } catch (error) {
            console.error('Error updating customer:', error);
            throw error;
        }
    }

    public static async deleteCustomer(customerId: number): Promise<boolean> {
        try {
            const customer = await customerRepository.findOneBy({ id: customerId });

            if (!customer) {
                return false;
            }

            await customerRepository.update(customerId, { is_active: false });
            return true;
        } catch (error) {
            console.error('Error deleting customer:', error);
            return false;
        }
    }
}
