"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const customerRepository_1 = require("../repositories/customerRepository");
const personRepository_1 = require("../repositories/personRepository");
class CustomerService {
    static getAllCustomers() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const customers = yield customerRepository_1.customerRepository.find({
                    relations: {
                        person: true
                    }
                });
                return customers;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static getCustomerById(customerId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const customer = yield customerRepository_1.customerRepository.findOne({
                    where: { id: customerId },
                    relations: {
                        person: true
                    }
                });
                return customer || null;
            }
            catch (error) {
                console.error('Error fetching customer:', error);
                throw error;
            }
        });
    }
    static createCustomer(customerData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const personData = {
                    fullname: customerData.fullname,
                    contact: customerData.contact,
                    cpf: customerData.cpf,
                    cnpj: customerData.cnpj,
                    personType: customerData.personType
                };
                const newPerson = personRepository_1.personRepository.create(personData);
                const savedPerson = yield personRepository_1.personRepository.save(newPerson);
                const newCustomerData = {
                    person: savedPerson,
                    address: customerData.address,
                    is_active: true
                };
                const newCustomer = customerRepository_1.customerRepository.create(newCustomerData);
                const savedCustomer = yield customerRepository_1.customerRepository.save(newCustomer);
                return savedCustomer;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static updateCustomer(customerId, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const customer = yield customerRepository_1.customerRepository.findOne({
                    where: { id: customerId },
                    relations: {
                        person: true
                    }
                });
                if (!customer) {
                    return false;
                }
                const personUpdatedData = Object.assign(Object.assign(Object.assign(Object.assign({}, (updateData.fullname && { fullname: updateData.fullname })), (updateData.contact && { contact: updateData.contact })), (updateData.cpf !== undefined && { cpf: updateData.cpf })), (updateData.cnpj !== undefined && { cnpj: updateData.cnpj }));
                const customerUpdatedData = Object.assign({}, (updateData.address && { address: updateData.address }));
                const wasPersonUpdated = Object.keys(personUpdatedData).length > 0;
                const wasCustomerUpdated = Object.keys(customerUpdatedData).length > 0;
                if (!wasPersonUpdated && !wasCustomerUpdated) {
                    return false;
                }
                const personId = customer.person.id;
                if (wasPersonUpdated) {
                    yield personRepository_1.personRepository.update(personId, personUpdatedData);
                }
                if (wasCustomerUpdated) {
                    yield customerRepository_1.customerRepository.update(customerId, customerUpdatedData);
                }
                return true;
            }
            catch (error) {
                console.error('Error updating customer:', error);
                throw error;
            }
        });
    }
    static deleteCustomer(customerId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const customer = yield customerRepository_1.customerRepository.findOneBy({ id: customerId });
                if (!customer) {
                    return false;
                }
                yield customerRepository_1.customerRepository.update(customerId, { is_active: false });
                return true;
            }
            catch (error) {
                console.error('Error deleting customer:', error);
                return false;
            }
        });
    }
}
exports.CustomerService = CustomerService;
