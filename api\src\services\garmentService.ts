import { ICreateGarmentDto, IPatchGarmentDto } from '../dto/GarmentDto';
import { Garment } from '../entities/Garment';
import { garmentRepository } from '../repositories/garmentRepository';

export class GarmentService {
    
    public static async getAllGarments(): Promise<Garment[]> {
        try {
            const garments = await garmentRepository.find();
            return garments;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async getGarmentById(garmentId: number): Promise<Garment | null> {
        try {
            const garment = await garmentRepository.findOneBy({ id: garmentId });
            return garment || null;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async createGarment(garmentData: {
        name: string;
        refcode: string;
        price: number;
        size: string;
        color: string;
        in_stock?: number;
    }): Promise<Garment> {
        try {
            // Check if refcode already exists
            const garmentByRefCode = await garmentRepository.findOneBy({ refcode: garmentData.refcode });
            if (garmentByRefCode) {
                throw new Error('Código de referência (Ref Code) já registrado em outro modelo anterior!');
            }

            const newGarmentData: ICreateGarmentDto = {
                name: garmentData.name,
                refcode: garmentData.refcode,
                price: garmentData.price,
                size: garmentData.size,
                color: garmentData.color,
                in_stock: garmentData.in_stock || 0,
                is_active: true
            };

            const newGarment = garmentRepository.create(newGarmentData);
            const savedGarment = await garmentRepository.save(newGarment);

            return savedGarment;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async updateGarment(garmentId: number, updateData: {
        name?: string;
        refcode?: string;
        price?: number;
        size?: string;
        color?: string;
        in_stock?: number;
    }): Promise<boolean> {
        try {
            const existingGarment = await garmentRepository.findOneBy({ id: garmentId });
            if (!existingGarment) {
                return false;
            }

            // Validate stock cannot be negative
            if (updateData.in_stock !== undefined && updateData.in_stock < 0) {
                throw new Error('Estoque não pode ser negativo!');
            }

            const garmentUpdatedData: IPatchGarmentDto = {
                ...(updateData.name && { name: updateData.name }),
                ...(updateData.refcode && { refcode: updateData.refcode }),
                ...(updateData.price !== undefined && { price: updateData.price }),
                ...(updateData.size && { size: updateData.size }),
                ...(updateData.color && { color: updateData.color }),
                ...(updateData.in_stock !== undefined && { in_stock: updateData.in_stock })
            };

            if (!Object.keys(garmentUpdatedData).length) {
                return false;
            }

            // Check if refcode is being updated and if it already exists
            if (garmentUpdatedData.refcode) {
                const garmentByRefCode = await garmentRepository.findOneBy({ refcode: garmentUpdatedData.refcode });
                if (garmentByRefCode && garmentByRefCode.id !== garmentId) {
                    throw new Error('Código de referência (Ref Code) já registrado em outro modelo!');
                }
            }

            await garmentRepository.update(garmentId, garmentUpdatedData);
            return true;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async deleteGarment(garmentId: number): Promise<boolean> {
        try {
            const garment = await garmentRepository.findOneBy({ id: garmentId });

            if (!garment) {
                return false;
            }

            await garmentRepository.update(garmentId, { is_active: false });
            return true;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    public static async checkRefCode(refcode: string): Promise<boolean> {
        try {
            const garment = await garmentRepository.findOneBy({ refcode });
            return !!garment;
        } catch (error) {
            console.error('Error checking refcode:', error);
            throw error;
        }
    }

    public static async validateGarmentData(name: string, refcode: string, price: number, size: string, color: string): Promise<boolean> {
        if (!name || !refcode || !price || !size || !color) {
            return false;
        }
        return true;
    }

    /**
     * Atualiza o estoque de forma segura adicionando ou subtraindo quantidade
     * Previne estoque negativo e fornece melhor tratamento de erros
     */
    public static async updateStock(garmentId: number, quantityChange: number, operation: 'add' | 'subtract'): Promise<boolean> {
        try {
            const existingGarment = await garmentRepository.findOneBy({ id: garmentId });
            if (!existingGarment) {
                throw new Error(`Peça com ID ${garmentId} não encontrada!`);
            }

            let newStock: number;
            if (operation === 'add') {
                newStock = existingGarment.in_stock + quantityChange;
            } else {
                newStock = existingGarment.in_stock - quantityChange;
            }

            // Valida que o estoque não pode ser negativo
            if (newStock < 0) {
                throw new Error(`Estoque insuficiente! Estoque atual: ${existingGarment.in_stock}, tentativa de ${operation === 'subtract' ? 'retirar' : 'adicionar'}: ${quantityChange}`);
            }

            await garmentRepository.update(garmentId, { in_stock: newStock });
            return true;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    /**
     * Verifica se há estoque suficiente para uma determinada quantidade
     */
    public static async checkStockAvailability(garmentId: number, requiredQuantity: number): Promise<{ available: boolean; currentStock: number; message?: string }> {
        try {
            const garment = await garmentRepository.findOneBy({ id: garmentId });
            if (!garment) {
                return {
                    available: false,
                    currentStock: 0,
                    message: `Peça com ID ${garmentId} não encontrada!`
                };
            }

            const available = garment.in_stock >= requiredQuantity;
            return {
                available,
                currentStock: garment.in_stock,
                message: available ? undefined : `Estoque insuficiente! Disponível: ${garment.in_stock}, necessário: ${requiredQuantity}`
            };
        } catch (error) {
            console.error(error);
            throw error;
        }
    }
}
