{"name": "view", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^7.0.1", "react-text-mask": "^5.5.0", "styled-components": "^6.1.13", "view": "file:"}, "devDependencies": {"@biomejs/biome": "1.9.3", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/react-text-mask": "^5.4.14", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "^5.5.3", "vite": "^5.4.8"}}