import { useEffect, useState } from "react";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";
import { api } from "../../../service/api";

interface Order {
	id: number;
	customer_id: number;
	staff_id: number;
	deadline: string;
	date: string;
	time: string;
	status: string;
	garments: Array<{
		id: number;
		quantity: number;
	}>;
}

interface Garment {
	id: number;
	name: string;
	refcode: string;
	price: string;
	size: string;
	color: string;
}

interface staffUser {
	id: number;
	username: string;
	userType: "staff" | "admin";
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
	staff?: {
		id: number;
		comissionRate: string;
	};
}

interface ClientModel {
	id: number;
	address: string;
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
}

export function SalesList() {
	const [orders, setOrders] = useState<Order[]>([]);
	const [staff, setStaff] = useState<staffUser[]>([]);
	const [garments, setGarments] = useState<Garment[]>([]);
	const [custumer, setCustumer] = useState<ClientModel[]>([]);
	const [loading, setLoading] = useState(true);
	useEffect(() => {
		async function fetchData() {
			try {
				const [ordersRes, staffRes, garmentRes, custumerRes] =
					await Promise.all([
						api.get("/orders"),
						api.get("/users"),
						api.get("/garments"),
						api.get("/customers"),
					]);

				setOrders(ordersRes.data);
				setStaff(staffRes.data);
				setGarments(garmentRes.data);
				setCustumer(custumerRes.data);
			} catch (error) {
				console.error("Erro ao carregar dados:", error);
			} finally {
				setLoading(false);
			}
		}
		fetchData();
	}, []);

	useEffect(() => {
		async function fetchData() {
			try {
				const [ordersRes, staffRes, garmentRes, custumerRes] =
					await Promise.all([
						api.get("/orders"),
						api.get("/users"),
						api.get("/garments"),
						api.get("/customers"),
					]);

				const sortedOrders = ordersRes.data.sort(
					(a: Order, b: Order) => b.id - a.id,
				);

				setOrders(sortedOrders);
				setStaff(staffRes.data);
				setGarments(garmentRes.data);
				setCustumer(custumerRes.data);
			} catch (error) {
				console.error("Erro ao carregar dados:", error);
			} finally {
				setLoading(false);
			}
		}
		fetchData();
	}, []);

	if (loading) {
		return <p>Carregando dados...</p>;
	}

	function getStaffDetailsById(staff_id: number) {
		if (staff.length === 0)
			return { fullname: "Carregando...", comissionRate: 0 };
		const staffMember = staff.find((s) => s.id === staff_id);
		return staffMember
			? {
				fullname: staffMember.person.fullname,
				comissionRate: Number(staffMember.staff?.comissionRate) || 0,
			}
			: { fullname: "Desconhecido", comissionRate: 0 };
	}

	function getGarmentDetailsById(garmentId: number) {
		return garments.find((g) => g.id === garmentId);
	}

	function getCustomerDetailsById(customer_id: number) {
		if (custumer.length === 0) return { fullname: "Carregando..." };
		const customer = custumer.find((c) => c.id === customer_id);
		return customer
			? { fullname: customer.person.fullname }
			: { fullname: "Desconhecido" };
	}

	function getStatusClass(status: string) {
		switch (status) {
			case "Fila de Espera":
				return "text-blue-500 bg-blue-50 border-blue-500";
			case "Em Produção":
				return "text-blue-500 bg-blue-50 border-blue-500";
			case "Concluído":
				return "text-green-500 bg-green-50 border-green-500";
			default:
				return "text-gray-500 bg-gray-50 border-gray-500";
		}
	}

	return (
		<>
			<div
				id="appContent"
				className="min-h-screen flex flex-col max-w-screen overflow-x-hidden"
			>
				<Header pagename={"Lista de Vendas"} href={"/"} $logout={false} />

				<main className="flex-grow flex gap-6 flex-col items-center py-8 px-4 text-lg">
					{orders.map((order) => {
						const { fullname, comissionRate } = getStaffDetailsById(
							order.staff_id,
						);

						const { fullname: customerName } = getCustomerDetailsById(
							order.customer_id,
						);

						const statusClass = getStatusClass(order.status);

						return (
							<div
								className="saleItem border border-gray-300 rounded-lg p-6  w-full max-w-5xl overflow-x-hidden"
								key={order.id}
							>
								<div id="saleData" className="flex flex-col items-center mb-6">
									<h2 className="font-semibold text-xl mb-2">
										Pedido {order.id}
									</h2>
									<div className="line-wrapper flex flex-col items-center sm:flex-row justify-around w-full mb-2 text-gray-700">
										<p>
											<span className="font-semibold">Status:</span>{" "}
											<span className={statusClass}>{order.status}</span>
										</p>
										<p>
											<span className="font-semibold">
												Vendedor: {fullname}
											</span>
										</p>
									</div>
									<p className="mb-1">
										<span className="font-semibold">Data e Hora:</span>{" "}
										<span>
											{order.date} às {order.time}
										</span>
									</p>
									<p>
										<span className="font-semibold">Cliente:</span>{" "}
										<span>{customerName}</span>
									</p>
								</div>

								<h3 className="font-semibold text-lg mb-4">Itens:</h3>

								{order.garments.map((garment) => {
									const garmentDetails = getGarmentDetailsById(garment.id);

									const unitPrice = Number(garmentDetails?.price) || 0;
									const totalPrice = unitPrice * garment.quantity;
									const commission = (totalPrice * comissionRate) / 100;

									return (
										<div
											key={garment.id}
											className="saleItems border border-gray-300 rounded-md p-4 mb-4"
										>
											<div className="line-wrapper flex flex-col items-center sm:flex-row justify-around mb-2">
												<p>
													<span className="font-semibold">Ref:</span>{" "}
													<span>#{garmentDetails?.refcode || "N/A"}</span>
												</p>
												<p>
													<span className="font-semibold">Cor:</span>{" "}
													<span>{garmentDetails?.color || "N/A"}</span>
												</p>
												<p>
													<span className="font-semibold">Tamanho:</span>{" "}
													<span>{garmentDetails?.size || "N/A"}</span>
												</p>
											</div>

											<div className="line-wrapper flex flex-col items-center sm:flex-row justify-around mb-2">
												<p>
													<span className="font-semibold">Quantidade:</span>{" "}
													<span>{garment.quantity}</span>
												</p>
												<p>
													<span className="font-semibold">Valor Unitário:</span>{" "}
													<span>
														{unitPrice.toLocaleString("pt-BR", {
															style: "currency",
															currency: "BRL",
														})}
													</span>
												</p>
											</div>

											<div className="line-wrapper flex flex-col items-center sm:flex-row justify-around">
												<p>
													<span className="font-semibold">Valor total:</span>{" "}
													<span>
														{totalPrice.toLocaleString("pt-BR", {
															style: "currency",
															currency: "BRL",
														})}
													</span>
												</p>
												<p>
													<span className="font-semibold">
														Comissão ({comissionRate}%):
													</span>{" "}
													<span>
														{commission.toLocaleString("pt-BR", {
															style: "currency",
															currency: "BRL",
														})}
													</span>
												</p>
											</div>
										</div>
									);
								})}
							</div>
						);
					})}
				</main>

				<Footer />
			</div>
		</>
	);
}
