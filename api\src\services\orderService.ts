import { ICreateOrderDto, IUpdateOrderDto } from '../dto/OrderDto';
import { Order } from '../entities/Order';
import { OrderHasGarments } from '../entities/OrderHasGarments';
import { customerRepository } from '../repositories/customerRepository';
import { garmentRepository } from '../repositories/garmentRepository';
import { orderHasGarmentsRepository } from '../repositories/orderHasGamentsRepository';
import { orderRepository } from '../repositories/orderRepository';
import { userRepository } from '../repositories/userRepository';

export class OrderService {

    public static async getAllOrders() {
        try {
            const orders = await orderRepository.find({
                relations: {
                    customer: {
                        person: true
                    },
                    user: {
                        person: true
                    },
                    garments: {
                        garment: true
                    }
                }
            });
            return orders;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }

    public static async getAllOrdersFormatted() {
        try {
            const orders = await orderRepository.find({
                relations: {
                    customer: {
                        person: true
                    },
                    user: {
                        person: true
                    },
                    garments: {
                        garment: true
                    }
                }
            });

            // Transforma os pedidos para o formato esperado pelo frontend
            const formattedOrders = orders.map(order => ({
                id: order.id,
                customer_id: order.customer.id, // Frontend espera customer_id
                staff_id: order.user.id, // Frontend espera staff_id
                date: order.date,
                time: order.time,
                deadline: order.deadline,
                status: order.status,
                garments: order.garments.map(garment => ({
                    id: garment.garment.id,
                    quantity: garment.quantity
                }))
            }));

            return formattedOrders;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }

    public static async getOrderById(id: number) {
        try {
            const order = await orderRepository.findOne({
                where: { id },
                relations: {
                    customer: {
                        person: true
                    },
                    user: {
                        person: true
                    },
                    garments: {
                        garment: true
                    }
                }
            });
            return order;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }

    public static async createOrder(orderData: ICreateOrderDto) {
        try {
            // Validate customer exists
            const customer = await customerRepository.findOneBy({ id: orderData.customer.id });
            if (!customer) {
                throw new Error(`Customer with id ${orderData.customer.id} not found`);
            }

            // Validate user exists
            const user = await userRepository.findOneBy({ id: orderData.user.id });
            if (!user) {
                throw new Error(`User with id ${orderData.user.id} not found`);
            }

            // Create order
            const order = new Order();
            order.date = orderData.date;
            order.time = orderData.time;
            order.deadline = orderData.deadline;
            order.status = orderData.status;
            order.customer = customer;
            order.user = user;

            const savedOrder = await orderRepository.save(order);

            // Create order-garment relationships
            for (const garmentData of orderData.garments) {
                const garment = await garmentRepository.findOneBy({ id: garmentData.id });
                if (!garment) {
                    throw new Error(`Garment with id ${garmentData.id} not found`);
                }

                const orderHasGarment = new OrderHasGarments();
                orderHasGarment.order = savedOrder;
                orderHasGarment.garment = garment;
                orderHasGarment.order_id = savedOrder.id;
                orderHasGarment.garment_id = garment.id;
                orderHasGarment.quantity = garmentData.quantity;

                await orderHasGarmentsRepository.save(orderHasGarment);
            }

            return savedOrder;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async createOrderFromRequest(requestData: {
        customer_id: number;
        staff_id: number;
        deadline: string;
        status: string;
        garments: { id: number; quantity: number }[];
    }) {
        try {
            // Validate required fields
            if (!requestData.deadline) {
                throw new Error('Prazo não foi definido!');
            }

            if (!requestData.status) {
                throw new Error('Status não foi definido!');
            }

            if (!Array.isArray(requestData.garments) || requestData.garments.length === 0) {
                throw new Error('É necessário incluir ao menos 1 modelo no pedido!');
            }

            // Validate customer exists
            const customer = await customerRepository.findOneBy({ id: requestData.customer_id });
            if (!customer) {
                throw new Error('Cliente não encontrado!');
            }

            // Validate user exists
            const staff = await userRepository.findOneBy({ id: requestData.staff_id });
            if (!staff) {
                throw new Error('Funcionário não encontrado!');
            }

            const date: Date = new Date();
            const time: string = new Date().toLocaleTimeString();

            const orderData: ICreateOrderDto = {
                customer,
                user: staff,
                date,
                time,
                deadline: requestData.deadline,
                status: requestData.status,
                garments: requestData.garments
            };

            return await this.createOrder(orderData);
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async updateOrder(id: number, updateData: IUpdateOrderDto) {
        try {
            const order = await orderRepository.findOne({
                where: { id },
                relations: {
                    customer: true,
                    user: true,
                    garments: true
                }
            });

            if (!order) {
                return null;
            }

            // Update order fields
            if (updateData.date !== undefined) {
                order.date = updateData.date;
            }
            if (updateData.time !== undefined) {
                order.time = updateData.time;
            }
            if (updateData.deadline !== undefined) {
                order.deadline = updateData.deadline;
            }
            if (updateData.status !== undefined) {
                order.status = updateData.status;
            }

            // Update customer if provided
            if (updateData.customer_id !== undefined) {
                const customer = await customerRepository.findOneBy({ id: updateData.customer_id });
                if (!customer) {
                    throw new Error(`Customer with id ${updateData.customer_id} not found`);
                }
                order.customer = customer;
            }

            // Update user if provided
            if (updateData.staff_id !== undefined) {
                const user = await userRepository.findOneBy({ id: updateData.staff_id });
                if (!user) {
                    throw new Error(`User with id ${updateData.staff_id} not found`);
                }
                order.user = user;
            }

            const updatedOrder = await orderRepository.save(order);
            return updatedOrder;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async deleteOrder(id: number) {
        try {
            // First delete order-garment relationships
            await orderHasGarmentsRepository.delete({ order_id: id });

            // Then delete the order
            const result = await orderRepository.delete(id);
            return result.affected ? result.affected > 0 : false;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    public static async getOrdersByCustomerId(customerId: number) {
        try {
            const orders = await orderRepository.find({
                where: { customer: { id: customerId } },
                relations: {
                    customer: {
                        person: true
                    },
                    user: {
                        person: true
                    },
                    garments: {
                        garment: true
                    }
                }
            });
            return orders;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }

    public static async getOrdersByUserId(userId: number) {
        try {
            const orders = await orderRepository.find({
                where: { user: { id: userId } },
                relations: {
                    customer: {
                        person: true
                    },
                    user: {
                        person: true
                    },
                    garments: {
                        garment: true
                    }
                }
            });
            return orders;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }

    public static async getOrdersByStatus(status: string) {
        try {
            const orders = await orderRepository.find({
                where: { status },
                relations: {
                    customer: {
                        person: true
                    },
                    user: {
                        person: true
                    },
                    garments: {
                        garment: true
                    }
                }
            });
            return orders;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }
}
