import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";

import { api } from "../../../service/api";

import { FiPlus } from "react-icons/fi";

import MaskedInput from "react-text-mask";

import { IoClose } from "react-icons/io5";
import { AlertModal } from "../../../components/AlertModal";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

interface ClientModel {
	id: number;
	address: string;
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
}

const cpfMask = [
	/\d/,
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	"-",
	/\d/,
	/\d/,
];

const cnpjMask = [
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	"/",
	/\d/,
	/\d/,
	/\d/,
	/\d/,
	"-",
	/\d/,
	/\d/,
];

export function ClientList() {
	const [clients, setClients] = useState<ClientModel[]>([]);

	const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
	const [selectedClient, setSelectedClient] = useState<ClientModel | null>(
		null,
	);

	const [isCpfSelected, setIsCpfSelected] = useState(true);

	const [clientName, setClientFullname] = useState("");
	const [clientContact, setClientContact] = useState("");
	const [clientAddress, setClientAddress] = useState("");
	const [clientIdentification, setClientIdentification] = useState("");

	const [searchTerm, setSearchTerm] = useState<string>("");

	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	const openModal = (client: ClientModel): void => {
		setSelectedClient(client);
		setClientFullname(client.person.fullname);
		setClientContact(client.person.contact);
		setClientAddress(client.address);

		if (client.person.personType === "individual") {
			setIsCpfSelected(true);
			setClientIdentification(client.person.cpf || "");
		} else {
			setIsCpfSelected(false);
			setClientIdentification(client.person.cnpj || "");
		}

		setIsModalOpen(true);
		document.body.style.overflow = "hidden";
	};

	const closeModal = (): void => {
		setSelectedClient(null);
		setIsModalOpen(false);
		document.body.style.overflow = "";
	};

	async function handleUpdateClient(
		event: React.MouseEvent<HTMLButtonElement>,
		client: ClientModel,
	) {
		event.preventDefault();

		try {
			if (!clientName || clientName === "") {
				setAlertMessage(
					"Preencha o campo de nome do cliente e tente novamente...",
				);
				setAlertType("error");
				setAlertCallback(null);
				setIsAlertModalOpen(true);
				return;
			}

			if (!clientContact || clientContact === "") {
				setAlertMessage(
					"Preencha o campo de contato do cliente e tente novamente...",
				);
				setAlertType("error");
				setAlertCallback(null);
				setIsAlertModalOpen(true);
				return;
			}

			if (!clientAddress || clientAddress === "") {
				setAlertMessage(
					"Preencha o campo de endereço do cliente e tente novamente...",
				);
				setAlertType("error");
				setAlertCallback(null);
				setIsAlertModalOpen(true);
				return;
			}

			if (!clientIdentification || clientIdentification === "") {
				setAlertMessage(
					"Preencha o campo de CPF/CNPJ do cliente corretamente e tente novamente...",
				);
				setAlertType("error");
				setAlertCallback(null);
				setIsAlertModalOpen(true);
				return;
			}

			const personType = isCpfSelected ? "individual" : "legal";
			const identificationField = isCpfSelected
				? { cpf: clientIdentification, cnpj: null }
				: { cnpj: clientIdentification, cpf: null };

			await api.patch(`/customers/${client.id}`, {
				fullname: clientName,
				contact: clientContact,
				...identificationField,
				personType: personType,
				address: clientAddress,
			});

			setClients((prevClients) =>
				prevClients.map((currentClient) =>
					currentClient.id === client.id
						? {
							...currentClient,
							address: clientAddress,
							person: {
								...currentClient.person,
								fullname: clientName,
								contact: clientContact,
								personType: personType,
								...identificationField,
							}
						}
						: currentClient,
				),
			);

			const handleSuccess = () => {
				setIsAlertModalOpen(false);
				closeModal();
			};

			setAlertMessage("Cliente atualizado com sucesso!");
			setAlertType("success");
			setAlertCallback(() => handleSuccess);
			setIsAlertModalOpen(true);
		} catch (error) {
			setAlertMessage("Não foi possível atualizar o cliente.");
			setAlertType("error");
			setAlertCallback(null);
			setIsAlertModalOpen(true);
			console.error(error);
		}
	}

	async function handleDeleteClient(id: number) {
		const handleConfirmDelete = async () => {
			try {
				await api.delete(`/customers/${id}`);
				setClients(clients.filter((client) => client.id !== id));

				const handleSuccess = () => {
					setIsAlertModalOpen(false);
				};

				setAlertMessage("Cliente excluído com sucesso!");
				setAlertType("success");
				setAlertCallback(() => handleSuccess);
				setIsAlertModalOpen(true);
			} catch (error) {
				setAlertMessage("Não foi possível excluir o cliente.");
				setAlertType("error");
				setAlertCallback(null);
				setIsAlertModalOpen(true);
				console.error(error);
			}
		};

		setAlertMessage("Tem certeza que deseja excluir esse cliente?");
		setAlertType("confirm");
		setAlertCallback(() => handleConfirmDelete);
		setIsAlertModalOpen(true);
	}

	useEffect(() => {
		async function getClients() {
			try {
				const response = await api.get("/customers");
				const allClients = response.data;

				// Se o termo de busca tem menos de 3 caracteres, mostra todos os clientes
				// Se tem 3 ou mais caracteres, aplica o filtro de busca
				if (searchTerm.trim().length >= 3) {
					const filteredClients = allClients.filter((client: ClientModel) =>
						client.person.fullname.toLowerCase().includes(searchTerm.toLowerCase()),
					);
					setClients(filteredClients);
				} else {
					setClients(allClients);
				}
			} catch (error) {
				console.error("Erro ao buscar clientes:", error);
			}
		}
		getClients();
	}, [searchTerm]);

	return (
		<>
			<div className="flex flex-col min-h-screen">
				<Header pagename={"Lista de Clientes"} href={"/"} $logout={false} />

				<main className="flex-grow text-center flex flex-col md:text-lg">
					<Link
						to={"/novo-cliente"}
						type="button"
						className="flex items-center gap-2.5 mx-auto mt-6 font-bold text-lg"
					>
						Adicionar Cliente <FiPlus className="text-3xl" />
					</Link>

					<input
						type="text"
						placeholder="Buscar por nome do cliente... (3 caracteres mínimo)"
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="border border-gray-300 rounded-md h-10 my-6 w-2/3 px-2 text-lg mx-auto"
					/>

					<div
						id="ClientListRender"
						className="px-2 grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mb-2"
					>
						{clients.length > 0 ? (
							clients.map((client) => (
								<div
									id="clientItem"
									className="border border-gray-300 rounded-md w-72 mx-auto flex flex-col text-left p-2 mb-2 relative"
									key={client.id}
								>
									<IoClose
										onClick={() => handleDeleteClient(client.id)}
										className="text-red-600 absolute top-2 right-3 cursor-pointer text-2xl"
									/>
									<h3 className="text-center text-lg truncate">
										{client.person.fullname}
									</h3>
									<span className="truncate">
										<strong>CPF/CNPJ:</strong> {client.person.cpf || client.person.cnpj || "Não informado"}
									</span>
									<span className="truncate">
										<strong>Endereço:</strong> {client.address || "Não informado"}
									</span>

									<p className="truncate">
										<strong>Contato:</strong>{" "}
										{client.person.contact || "Não informado"}
									</p>

									<span className="flex w-full justify-end">
										<button
											type="button"
											className="border border-gray-300 rounded bg bg-blue-200 text-sm px-4 w-24"
											onClick={() => openModal(client)}
										>
											Detalhes
										</button>
									</span>
								</div>
							))
						) : (
							<div className="col-span-full text-center text-xl text-gray-500 py-8">
								{searchTerm.trim().length >= 3
									? `Nenhum cliente encontrado com o termo "${searchTerm}"`
									: "Nenhum cliente cadastrado"
								}
							</div>
						)}
					</div>

					{isModalOpen && selectedClient && (
						<div className="modal-overlay fixed inset-0 flex items-center justify-center backdrop-blur bg-black bg-opacity-50 px-1 rounded-md">
							<form className="w-full max-w-xl bg-white border border-gray-300 rounded-md relative text-left">
								<fieldset className="p-4 flex flex-col gap-4">
									<IoClose
										onClick={closeModal}
										className="text-red-700 absolute top-3 right-4 cursor-pointer text-2xl"
									/>
									{/* Nome */}
									<div className="flex flex-col">
										<label htmlFor="client-name" className="mb-1">
											Nome do Cliente:
										</label>
										<input
											type="text"
											id="client-name"
											className="p-3 border border-gray-300 rounded-md px-2 py-1"
											value={clientName}
											onChange={(e) => setClientFullname(e.target.value)}
										/>
									</div>

									{/* Endereço */}
									<div className="flex flex-col">
										<label htmlFor="adress" className="mb-1">
											Endereço:
										</label>
										<input
											type="text"
											id="adress"
											className="p-3 border border-gray-300 rounded-md px-2 py-1"
											value={clientAddress}
											onChange={(e) => setClientAddress(e.target.value)}
										/>
									</div>

									{/* CPF / CNPJ */}
									<div className="flex flex-col">
										<div className="input-wrapper flex items-center space-x-4 mb-1.5">
											<div className="check flex items-center gap-1">
												<input
													type="radio"
													name="identification"
													id="cpfcheck"
													checked={isCpfSelected}
													onChange={() => setIsCpfSelected(true)}
													className="form-radio w-4 h-4"
												/>
												<label htmlFor="cpfcheck">CPF:</label>
											</div>
											<div className="check flex items-center gap-1">
												<input
													type="radio"
													name="identification"
													id="cnpjcheck"
													checked={!isCpfSelected}
													onChange={() => setIsCpfSelected(false)}
													className="form-radio w-5 h-5"
												/>
												<label htmlFor="cnpjcheck">CNPJ:</label>
											</div>
										</div>

										{isCpfSelected ? (
											<div className="input-wrapper">
												<label htmlFor="cpfnum" className="block mb-1.5">
													CPF:
												</label>
												<MaskedInput
													mask={cpfMask}
													size={11}
													id="cpfnum"
													placeholder="___.___.___-__"
													className="w-full p-3 border border-gray-300 rounded-md px-2 py-1"
													value={clientIdentification}
													onChange={(e) =>
														setClientIdentification(e.target.value)
													}
												/>
											</div>
										) : (
											<div className="input-wrapper">
												<label htmlFor="cnpjnum" className="block mb-1.5">
													CNPJ:
												</label>
												<MaskedInput
													mask={cnpjMask}
													size={14}
													id="cnpjnum"
													placeholder="__.___.___/____-__"
													className="w-full p-3 border border-gray-300 rounded-md px-2 py-1"
													value={clientIdentification}
													onChange={(e) =>
														setClientIdentification(e.target.value)
													}
												/>
											</div>
										)}
									</div>

									{/* Contato */}
									<div className="flex flex-col">
										<label htmlFor="contactinfo" className="mb-2">
											Contato:
										</label>
										<input
											type="text"
											id="contactinfo"
											className="p-3 border border-gray-300 rounded-md px-2 py-1"
											value={clientContact}
											onChange={(e) => setClientContact(e.target.value)}
										/>
									</div>

									<div className="flex w-full justify-center">
										<button
											type="button"
											className="bg-blue-300 border border-gray-300 rounded px-6 py-2 mt-2"
											onClick={(event) =>
												// biome-ignore lint/style/noNonNullAssertion: <explanation>
												handleUpdateClient(event, selectedClient!)
											}
										>
											Confirmar atualização
										</button>
									</div>
								</fieldset>
							</form>
						</div>
					)}
				</main>

				<Footer />
			</div>
			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => setIsAlertModalOpen(false)}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</>
	);
}
