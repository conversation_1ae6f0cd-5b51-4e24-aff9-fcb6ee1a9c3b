# Raw Material API Routes

## Base URL
- `/raw-materials` - <PERSON><PERSON> as rotas seguem o padrão RESTful

## Endpoints

### 1. GET /raw-materials
**Descrição:** Busca todas as matérias-primas ativas

**Resposta de Sucesso (200):**
```json
[
  {
    "id": 1,
    "name": "Algodão",
    "manufacturer": "Tecidos Brasil",
    "price_per_weight": 10.50,
    "color": "Branco",
    "weight": 500,
    "is_active": true
  }
]
```

### 2. GET /raw-materials/:id
**Descrição:** Busca uma matéria-prima específica por ID

**Parâmetros:**
- `id` (number) - ID da matéria-prima

**Resposta de Sucesso (200):**
```json
{
  "id": 1,
  "name": "Algodão",
  "manufacturer": "Tecidos Brasil",
  "price_per_weight": 10.50,
  "color": "Branco",
  "weight": 500,
  "is_active": true
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Matéria-prima não encontrada"
}
```

### 3. POST /raw-materials
**Descrição:** Cria uma nova matéria-prima

**Body:**
```json
{
  "name": "Algodão",
  "manufacturer": "Tecidos Brasil",
  "pricePerKg": 10.50,
  "color": "Branco",
  "weight": 500
}
```

**Resposta de Sucesso (201):**
```json
{
  "message": "Matéria-prima registrada com sucesso!",
  "rawMaterial": {
    "id": 1,
    "name": "Algodão",
    "manufacturer": "Tecidos Brasil",
    "price_per_weight": 10.50,
    "color": "Branco",
    "weight": 500,
    "is_active": true
  }
}
```

### 4. PATCH /raw-materials/:id
**Descrição:** Atualiza uma matéria-prima existente

**Parâmetros:**
- `id` (number) - ID da matéria-prima

**Body (campos opcionais):**
```json
{
  "name": "Novo Nome",
  "manufacturer": "Novo Fabricante",
  "pricePerKg": 12.00,
  "color": "Nova Cor",
  "weight": 600
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Matéria-prima atualizada com sucesso!",
  "rawMaterial": {
    "id": 1,
    "name": "Novo Nome",
    "manufacturer": "Novo Fabricante",
    "price_per_weight": 12.00,
    "color": "Nova Cor",
    "weight": 600,
    "is_active": true
  }
}
```

### 5. DELETE /raw-materials/:id
**Descrição:** Remove uma matéria-prima (soft delete - marca como inativa)

**Parâmetros:**
- `id` (number) - ID da matéria-prima

**Resposta de Sucesso (200):**
```json
{
  "message": "Matéria-prima removida com sucesso!"
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Matéria-prima não encontrada"
}
```

## Códigos de Erro Comuns

- **400 Bad Request:** Dados inválidos ou campos obrigatórios não preenchidos
- **404 Not Found:** Matéria-prima não encontrada
- **500 Internal Server Error:** Erro interno do servidor

## Validações

### Campos Obrigatórios (POST):
- `name` (string)
- `manufacturer` (string)
- `pricePerKg` (number > 0)
- `color` (string)
- `weight` (number > 0)

### Campos Opcionais (PATCH):
- Todos os campos são opcionais
- Valores numéricos devem ser maiores que zero quando fornecidos
