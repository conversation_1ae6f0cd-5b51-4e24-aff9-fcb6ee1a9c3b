# Garment API Routes

## Base URL
- `/garments` - Todas as rotas seguem o padrão RESTful

## Endpoints

### 1. GET /garments
**Descrição:** Busca todos os modelos/peças ativas

**Resposta de Sucesso (200):**
```json
[
  {
    "id": 1,
    "refcode": "102050",
    "name": "Camiseta Branca",
    "color": "Branco",
    "size": "M",
    "price": 25.00,
    "is_active": true,
    "in_stock": 15
  }
]
```

### 2. GET /garments/:id
**Descrição:** Busca um modelo/peça específico por ID

**Parâmetros:**
- `id` (number) - ID do modelo/peça

**Resposta de Sucesso (200):**
```json
{
  "id": 1,
  "refcode": "102050",
  "name": "Camiseta Branca",
  "color": "Branco",
  "size": "M",
  "price": 25.00,
  "is_active": true,
  "in_stock": 15
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Modelo não encontrado"
}
```

### 3. POST /garments
**Descrição:** Cria um novo modelo/peça

**Body:**
```json
{
  "refcode": "102050",
  "name": "Camiseta Branca",
  "color": "Branco",
  "size": "M",
  "price": 25.00,
  "in_stock": 15
}
```

**Resposta de Sucesso (201):**
```json
{
  "message": "Modelo registrado com sucesso!",
  "Garment": {
    "id": 1,
    "refcode": "102050",
    "name": "Camiseta Branca",
    "color": "Branco",
    "size": "M",
    "price": 25.00,
    "is_active": true,
    "in_stock": 15
  }
}
```

**Resposta de Erro (400):**
```json
{
  "error": "Há campo(s) não preenchido(s)!"
}
```

**Resposta de Erro (400) - Código duplicado:**
```json
{
  "error": "Código de referência já existe no sistema"
}
```

### 4. PATCH /garments/:id
**Descrição:** Atualiza um modelo/peça existente

**Parâmetros:**
- `id` (number) - ID do modelo/peça

**Body (campos opcionais):**
```json
{
  "name": "Camiseta Branca Premium",
  "color": "Off-White",
  "size": "G",
  "price": 30.00,
  "in_stock": 20
}
```

**Resposta de Sucesso (200):**
```json
{
  "message": "Modelo atualizado com sucesso!",
  "Garment": {
    "id": 1,
    "refcode": "102050",
    "name": "Camiseta Branca Premium",
    "color": "Off-White",
    "size": "G",
    "price": 30.00,
    "is_active": true,
    "in_stock": 20
  }
}
```

### 5. DELETE /garments/:id
**Descrição:** Remove um modelo/peça (soft delete - marca como inativo)

**Parâmetros:**
- `id` (number) - ID do modelo/peça

**Resposta de Sucesso (200):**
```json
{
  "message": "Modelo removido com sucesso!"
}
```

**Resposta de Erro (404):**
```json
{
  "error": "Modelo não encontrado"
}
```

## Códigos de Erro Comuns

- **400 Bad Request:** Dados inválidos ou campos obrigatórios não preenchidos
- **404 Not Found:** Modelo não encontrado
- **500 Internal Server Error:** Erro interno do servidor

## Validações

### Campos Obrigatórios (POST):
- `refcode` (string) - Código de referência único
- `name` (string) - Nome do modelo
- `color` (string) - Cor da peça
- `size` (string) - Tamanho da peça
- `price` (number > 0) - Preço da peça

### Campos Opcionais (POST):
- `in_stock` (number >= 0) - Quantidade em estoque (padrão: 0)

### Campos Opcionais (PATCH):
- Todos os campos são opcionais
- Valores numéricos devem ser maiores que zero quando fornecidos
- `in_stock` pode ser zero ou maior

### Regras de Negócio:
- `refcode` deve ser único no sistema
- `price` deve ser maior que zero
- `in_stock` deve ser zero ou maior
- Campos de texto têm limites de caracteres:
  - `refcode`: máximo 50 caracteres
  - `name`: máximo 100 caracteres
  - `color`: máximo 50 caracteres
  - `size`: máximo 50 caracteres
