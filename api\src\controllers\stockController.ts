import type { Request, Response } from 'express';
import { GarmentService } from '../services/garmentService';
import { StockService } from '../services/stockService';

interface StockUpdateRequest {
    garmentId: number;
    quantity: number;
    operation: 'add' | 'subtract' | 'set';
}

interface BatchStockUpdateRequest {
    operations: StockUpdateRequest[];
}

class StockController {

    /**
     * Atualiza o estoque de uma única peça
     */
    public static async updateStock(req: Request, res: Response) {
        try {
            const { garmentId, quantity, operation }: StockUpdateRequest = req.body;

            if (!garmentId || quantity === undefined || !operation) {
                return res.status(400).json({ 
                    error: 'Campos obrigatórios: garmentId, quantity, operation' 
                });
            }

            if (quantity < 0) {
                return res.status(400).json({ 
                    error: 'Quantidade não pode ser negativa' 
                });
            }

            if (!['add', 'subtract', 'set'].includes(operation)) {
                return res.status(400).json({ 
                    error: 'Operação deve ser: add, subtract ou set' 
                });
            }

            if (operation === 'set') {
                // Atualização direta do estoque
                const updated = await GarmentService.updateGarment(garmentId, { in_stock: quantity });
                if (!updated) {
                    return res.status(404).json({ error: 'Peça não encontrada' });
                }
            } else {
                // Operação de adicionar ou subtrair
                await GarmentService.updateStock(garmentId, quantity, operation);
            }

            return res.status(200).json({ 
                message: 'Estoque atualizado com sucesso!' 
            });

        } catch (error) {
            console.error('Error updating stock:', error);
            if (error instanceof Error) {
                return res.status(400).json({ error: error.message });
            }
            return res.status(500).json({ error: 'Erro interno do servidor' });
        }
    }

    /**
     * Atualiza o estoque de múltiplas peças em uma transação
     */
    public static async batchUpdateStock(req: Request, res: Response) {
        try {
            const { operations }: BatchStockUpdateRequest = req.body;

            if (!Array.isArray(operations) || operations.length === 0) {
                return res.status(400).json({ 
                    error: 'Lista de operações é obrigatória' 
                });
            }

            // Valida todas as operações
            for (const op of operations) {
                if (!op.garmentId || op.quantity === undefined || !op.operation) {
                    return res.status(400).json({
                        error: 'Cada operação deve ter: garmentId, quantity, operation'
                    });
                }

                if (op.quantity < 0) {
                    return res.status(400).json({
                        error: 'Quantidade não pode ser negativa'
                    });
                }

                if (!['add', 'subtract', 'set'].includes(op.operation)) {
                    return res.status(400).json({
                        error: 'Operação deve ser: add, subtract ou set'
                    });
                }
            }

            // Converte para o formato do StockService
            const stockOperations = operations
                .filter(op => op.operation !== 'set')
                .map(op => ({
                    garmentId: op.garmentId,
                    quantity: op.quantity,
                    operation: op.operation as 'add' | 'subtract'
                }));

            // Trata operações 'set' separadamente
            const setOperations = operations.filter(op => op.operation === 'set');

            // Executa operações 'set' primeiro
            for (const setOp of setOperations) {
                const updated = await GarmentService.updateGarment(setOp.garmentId, {
                    in_stock: setOp.quantity
                });
                if (!updated) {
                    return res.status(404).json({
                        error: `Peça com ID ${setOp.garmentId} não encontrada`
                    });
                }
            }

            // Executa operações add/subtract em transação
            if (stockOperations.length > 0) {
                await StockService.executeStockOperations(stockOperations);
            }

            return res.status(200).json({ 
                message: 'Operações de estoque executadas com sucesso!' 
            });

        } catch (error) {
            console.error('Error in batch stock update:', error);
            if (error instanceof Error) {
                return res.status(400).json({ error: error.message });
            }
            return res.status(500).json({ error: 'Erro interno do servidor' });
        }
    }

    /**
     * Valida a disponibilidade de estoque para conclusão de pedido
     */
    public static async validateOrderStock(req: Request, res: Response) {
        try {
            const { garments }: { garments: Array<{ id: number; quantity: number }> } = req.body;

            if (!Array.isArray(garments) || garments.length === 0) {
                return res.status(400).json({ 
                    error: 'Lista de peças é obrigatória' 
                });
            }

            const validation = await StockService.processOrderCompletion(garments);

            if (validation.valid) {
                return res.status(200).json({ 
                    message: 'Estoque suficiente para o pedido',
                    valid: true 
                });
            } else {
                return res.status(400).json({ 
                    error: 'Estoque insuficiente',
                    valid: false,
                    errors: validation.errors,
                    insufficientItems: validation.insufficientItems
                });
            }

        } catch (error) {
            console.error('Error validating order stock:', error);
            if (error instanceof Error) {
                return res.status(400).json({ error: error.message });
            }
            return res.status(500).json({ error: 'Erro interno do servidor' });
        }
    }

    /**
     * Obtém o status atual do estoque para múltiplas peças
     */
    public static async getStockStatus(req: Request, res: Response) {
        try {
            const { garmentIds }: { garmentIds: number[] } = req.body;

            if (!Array.isArray(garmentIds) || garmentIds.length === 0) {
                return res.status(400).json({ 
                    error: 'Lista de IDs de peças é obrigatória' 
                });
            }

            const stockStatus = await StockService.getStockStatus(garmentIds);

            return res.status(200).json(stockStatus);

        } catch (error) {
            console.error('Error getting stock status:', error);
            return res.status(500).json({ error: 'Erro interno do servidor' });
        }
    }
}

export default StockController;
