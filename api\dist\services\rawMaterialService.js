"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RawMaterialService = void 0;
const rawMaterialRepository_1 = require("../repositories/rawMaterialRepository");
class RawMaterialService {
    static getAllRawMaterials() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterials = yield rawMaterialRepository_1.rawMaterialRepository.find({
                    where: { is_active: true }
                });
                return rawMaterials;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static getRawMaterialById(rawMaterialId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterial = yield rawMaterialRepository_1.rawMaterialRepository.findOne({
                    where: { id: rawMaterialId, is_active: true }
                });
                return rawMaterial || null;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static createRawMaterial(rawMaterialData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const newRawMaterialData = {
                    name: rawMaterialData.name,
                    manufacturer: rawMaterialData.manufacturer,
                    price_per_weight: rawMaterialData.price_per_weight,
                    color: rawMaterialData.color,
                    weight: rawMaterialData.weight,
                    is_active: true
                };
                const newRawMaterial = rawMaterialRepository_1.rawMaterialRepository.create(newRawMaterialData);
                const savedRawMaterial = yield rawMaterialRepository_1.rawMaterialRepository.save(newRawMaterial);
                return savedRawMaterial;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static updateRawMaterial(rawMaterialId, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterial = yield rawMaterialRepository_1.rawMaterialRepository.findOne({
                    where: { id: rawMaterialId, is_active: true }
                });
                if (!rawMaterial) {
                    return null;
                }
                yield rawMaterialRepository_1.rawMaterialRepository.update(rawMaterialId, updateData);
                const updatedRawMaterial = yield rawMaterialRepository_1.rawMaterialRepository.findOneBy({ id: rawMaterialId });
                return updatedRawMaterial || null;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static deleteRawMaterial(rawMaterialId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const rawMaterial = yield rawMaterialRepository_1.rawMaterialRepository.findOne({
                    where: { id: rawMaterialId, is_active: true }
                });
                if (!rawMaterial) {
                    return false;
                }
                yield rawMaterialRepository_1.rawMaterialRepository.update(rawMaterialId, { is_active: false });
                return true;
            }
            catch (error) {
                console.error(error);
                return false;
            }
        });
    }
    static validateRawMaterialData(name, manufacturer, price_per_weight, color, weight) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!name || !manufacturer || price_per_weight === undefined || !color || weight === undefined) {
                return false;
            }
            if (price_per_weight <= 0 || weight <= 0) {
                return false;
            }
            return true;
        });
    }
}
exports.RawMaterialService = RawMaterialService;
